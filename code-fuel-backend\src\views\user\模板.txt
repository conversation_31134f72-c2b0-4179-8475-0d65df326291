<script setup>

</script>


<template>

  <div class="echarts-box">

  </div>

</template>


<style lang="scss" scoped>

</style>


<template>
  <div>
    <!-- 搜索框 -->
    <el-input
      v-model="searchKeyword"
      placeholder="输入关键词搜索"
      style="width: 300px; margin-bottom: 20px"
    />
    <el-button type="primary" @click="searchPosts">搜索</el-button>
    <el-button type="success" style="margin-left: 10px">我要发布</el-button>

    <!-- 帖子列表 -->
    <div v-for="post in displayedPosts" :key="post.id" class="post-item">
      <!-- 帖子内容 -->
      <!-- 类似你提供的图片结构 -->
      <div class="post-header">
        <el-avatar :size="40" :src="post.avatar" />
        <div class="user-info">
          <span class="username">{{ post.username }}</span>
          <span class="post-time">{{ formatTime(post.postTime) }}</span>
        </div>
        <el-tag v-if="post.isTop" type="warning" effect="dark" class="top-tag">
          置顶
        </el-tag>
      </div>
      <div class="post-content">
        <h2 class="post-title">{{ post.title }}</h2>
        <p class="post-excerpt">{{ post.content.slice(0, 200) }}{{ post.content.length > 200 ? '...' : '' }}</p>
      </div>
      <div class="post-footer">
        <span class="post-meta">
          <i class="iconfont icon-like"></i>
          {{ post.likeCount || 0 }} 赞
        </span>
        <span class="post-meta">
          <i class="iconfont icon-comment"></i>
          {{ post.commentCount || 0 }} 评论
        </span>
      </div>
    </div>

    <!-- 分页器 -->
    <div class="demo-pagination-block">
      <el-pagination
        v-model:current-page="pageNo"
        v-model:page-size="pageSize"
        style="margin-top: 15px"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="pageSizeChange"
        @current-change="pageNoChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 模拟帖子数据
const posts = ref([
  // 这里应该是从后端获取的帖子数据，包含 isTop 属性
  // 示例数据
  {
    id: 1,
    userId: 1,
    username: '王清楚',
    avatar: 'https://example.com/avatar1.jpg',
    title: '【新手上路】语法入门+算法入门题单',
    content: '这是帖子内容...',
    isTop: 1,
    postTime: '2021-12-06T10:00:00',
    likeCount: 122,
    commentCount: 845,
  },
  // 更多帖子数据...
]);

const searchKeyword = ref('');
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(posts.value.length); // 初始总条数为所有帖子数，实际应从后端获取

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  return new Date(time).toLocaleString();
};

// 计算显示的帖子列表，置顶帖子在前
const displayedPosts = computed(() => {
  // 分离置顶帖子和普通帖子
  const topPosts = posts.value.filter(post => post.isTop === 1);
  const normalPosts = posts.value.filter(post => post.isTop !== 1);

  // 合并并分页
  const combinedPosts = [...topPosts, ...normalPosts];
  const startIndex = (pageNo.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return combinedPosts.slice(startIndex, endIndex);
});

// 分页大小改变事件
const pageSizeChange = (newSize) => {
  pageSize.value = newSize;
};

// 当前页改变事件
const pageNoChange = (newPage) => {
  pageNo.value = newPage;
};

// 搜索功能（留出位置，后面补充具体功能）
const searchPosts = () => {
  console.log('搜索关键词:', searchKeyword.value);
  // 这里可以添加向后端发送搜索请求的逻辑
};
</script>

<style scoped>
/* 你的样式代码 */
.post-item {
  padding: 20px;
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
/* 其他样式... */
</style>