{"name": "template_vue3", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@stomp/stompjs": "^7.1.1", "@vueup/vue-quill": "^1.2.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^2.1.7", "pinia-persistedstate-plugin": "^0.1.0", "sockjs-client": "^1.6.1", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "sass": "^1.69.5", "vite": "^4.4.11"}}