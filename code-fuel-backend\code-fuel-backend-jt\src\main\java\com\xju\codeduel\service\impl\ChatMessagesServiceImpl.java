package com.xju.codeduel.service.impl;

import com.xju.codeduel.model.domain.ChatMessages;
import com.xju.codeduel.mapper.ChatMessagesMapper;
import com.xju.codeduel.model.dto.MessageWithUserDTO;
import com.xju.codeduel.service.IChatMessagesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 聊天消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class ChatMessagesServiceImpl extends ServiceImpl<ChatMessagesMapper, ChatMessages> implements IChatMessagesService {

    @Override
    public List<MessageWithUserDTO> getMessagesWithUsers(String codeforcesId) {
        return baseMapper.selectMessagesWithUsers(codeforcesId);
    }
}
