<script setup>

import {
  CaretBottom,
  SwitchButton,
  User,
  House,
  Trophy,
  Promotion,
  ChatDotRound,
  ChatLineRound,
  Setting,
  Monitor
} from '@element-plus/icons-vue'
import avatar from '@/assets/default.png'
import CodeDuelLogo from '@/components/CodeDuelLogo.vue'
import {useUserInfoStore} from "@/stores/userInfo";
import {useRouter} from "vue-router";
import {ElMessage, ElMessageBox} from "element-plus";
import {getInfo, getCurrentRoomStatus as apiGetCurrentRoomStatus, rejoinRoom as apiRejoinRoom, leaveCurrentRoom} from "@/api/api";
import {ref} from 'vue'

const userInfoStore = useUserInfoStore();
const activeIndex = ref('home')

// 用户当前房间状态
const currentRoomStatus = ref({
  hasRoom: false,
  roomCode: null,
  status: 'NONE',
  message: '当前没有参与任何房间'
})

const getUserInfo = ()=>{
  getInfo().then(
      res => {
        if(res.data == null)
        {
          ElMessage.error("暂未登录，请先登录")
          router.push('/login')
        }else{
          userInfoStore.setUserInfo(res.data)
          // 获取用户房间状态
          getUserRoomStatus()
        }
      }
  )
}

// 获取用户当前房间状态
const getUserRoomStatus = async () => {
  try {
    const response = await apiGetCurrentRoomStatus()
    if (response && response.status && response.data) {
      currentRoomStatus.value = response.data
      console.log('🔍 获取用户房间状态:', currentRoomStatus.value)
    } else {
      // 用户未登录或获取失败，重置状态
      currentRoomStatus.value = {
        hasRoom: false,
        roomCode: null,
        status: 'NONE',
        message: '当前没有参与任何房间'
      }
      console.log('📋 用户当前没有房间')
    }
  } catch (error) {
    console.error('❌ 获取房间状态失败:', error)
    // 出错时重置状态
    currentRoomStatus.value = {
      hasRoom: false,
      roomCode: null,
      status: 'NONE',
      message: '当前没有参与任何房间'
    }
  }
}

// 重新加入房间
const rejoinUserRoom = async () => {
  if (currentRoomStatus.value.hasRoom) {
    try {
      const response = await apiRejoinRoom(currentRoomStatus.value.roomCode)
      if (response.data.status) {
        router.push('/dashboard/battle')
        ElMessage.success(`正在返回房间 ${currentRoomStatus.value.roomCode}`)
      } else {
        ElMessage.error('重新加入房间失败: ' + response.data.message)
        // 重新获取房间状态
        getUserRoomStatus()
      }
    } catch (error) {
      console.error('❌ 重新加入房间失败:', error)
      ElMessage.error('重新加入房间失败')
      // 重新获取房间状态
      getUserRoomStatus()
    }
  }
}

getUserInfo()

const router = useRouter();
const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm(
        '你确认要退出吗？',
        '温馨提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
    ).then(
        async () => {
          try {
            // 如果用户在房间中，先离开房间
            if (currentRoomStatus.value.hasRoom) {
              console.log('🚪 退出登录前离开房间')
              await leaveCurrentRoom()
            }
          } catch (error) {
            console.error('❌ 离开房间失败:', error)
            // 即使离开房间失败，也继续退出登录流程
          }

          // clear data in pinia
          userInfoStore.removeUserInfo()

          // 清除房间状态
          currentRoomStatus.value = {
            hasRoom: false,
            roomCode: null,
            status: 'NONE',
            message: '当前没有参与任何房间'
          }

          ElMessage.success("退出成功")
          await router.push('/login')
        }
    )
  } else if (command === 'admin') {
    router.push('/admin/userlist')
  } else if (command === 'info') {
    // 跳转到基本资料页面
    router.push('/dashboard/user/info')
  } else {
    router.push('/user/' + command)
  }
}

const handleSelect = (key) => {
  activeIndex.value = key
  router.push('/' + key)
}
</script>

<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <!-- 左侧Logo -->
      <div class="logo-section">
        <CodeDuelLogo :width="170" :height="45" :show-subtitle="false" />
      </div>

      <!-- 中间导航菜单 -->
      <div class="nav-section">
        <el-menu
            :default-active="activeIndex"
            class="nav-menu"
            mode="horizontal"
            @select="handleSelect"
            background-color="transparent"
            text-color="#333"
            active-text-color="#409EFF"
            :collapse-transition="false"
            :ellipsis="false">
          <el-menu-item index="dashboard/home">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="dashboard/battle">
            <el-icon><Promotion /></el-icon>
            <span>对战</span>
          </el-menu-item>

          <!-- 我的对战按钮 -->
          <el-menu-item
            v-if="currentRoomStatus && currentRoomStatus.hasRoom"
            @click="rejoinUserRoom"
            class="my-battle-item"
            :class="{ 'battling': currentRoomStatus && currentRoomStatus.status === 'BATTLING' }">
            <el-icon><Monitor /></el-icon>
            <span>我的对战</span>
            <el-badge
              v-if="currentRoomStatus && currentRoomStatus.status === 'BATTLING'"
              is-dot
              class="battle-badge">
            </el-badge>
          </el-menu-item>

          <el-menu-item index="dashboard/ranking">
            <el-icon><Trophy /></el-icon>
            <span>排行榜</span>
          </el-menu-item>
          <el-menu-item index="dashboard/forum">
            <el-icon><ChatDotRound /></el-icon>
            <span>讨论区</span>
          </el-menu-item>
          <el-menu-item index="dashboard/chat">
            <el-icon><ChatLineRound /></el-icon>
            <span>聊天区</span>
          </el-menu-item>
          <el-menu-item :index="`dashboard/profile/${userInfoStore.userInfo.codeforcesId}`">
            <el-icon><User /></el-icon>
            <span>个人中心</span>
          </el-menu-item>

        </el-menu>
      </div>

      <!-- 右侧用户区域 -->
      <div class="user-section">
        <el-dropdown placement="bottom-end" @command="handleCommand">
          <span class="user-dropdown">
            <el-avatar
                :size="40"
                :src="userInfoStore.userInfo.avatar ? userInfoStore.userInfo.avatar : avatar"
                class="user-avatar"
            />
            <el-icon class="dropdown-icon">
              <CaretBottom/>
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="info" :icon="User">基本资料</el-dropdown-item>
              <!-- <el-dropdown-item command="resetPassword" :icon="EditPen">修改密码</el-dropdown-item> -->
              <el-dropdown-item command="admin" :icon="Setting" v-if="userInfoStore.userInfo.isAdmin">管理后台</el-dropdown-item>
              <el-dropdown-item command="logout" :icon="SwitchButton" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主内容区域 -->
    <el-main class="main-content">
      <router-view/>
    </el-main>


  </div>
</template>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .main-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px; // 减少左右内边距
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 1200px; // 设置最小宽度确保有足够空间

    .logo-section {
      flex-shrink: 0; // 防止logo区域被压缩
      width: 200px; // 固定logo区域宽度

      .logo {
        height: 40px;
        width: auto;
        max-width: 180px; // 限制logo最大宽度
      }
    }

    .nav-section {
      flex: 1;
      display: flex;
      justify-content: center;
      min-width: 0; // 允许flex项目收缩

      .nav-menu {
        border-bottom: none;
        width: 100%;
        max-width: 800px; // 设置最大宽度以容纳所有菜单项

        // 强制显示所有菜单项，禁用自动收缩
        :deep(.el-menu--horizontal) {
          display: flex !important;
          flex-wrap: nowrap !important;
          overflow: visible !important;

          .el-menu-item,
          .el-sub-menu {
            float: none !important;
            display: flex !important;
            flex-shrink: 0 !important;
          }

          // 隐藏更多按钮和相关元素
          .el-menu-item:last-child {
            display: flex !important;
          }
        }

        // 隐藏更多按钮
        :deep(.el-menu--horizontal .el-menu--popup),
        :deep(.el-menu--horizontal .el-menu-item.is-disabled),
        :deep(.el-menu--horizontal .el-sub-menu__icon-more) {
          display: none !important;
        }

        // 确保菜单项不会被隐藏
        :deep(.el-menu-item) {
          display: flex !important;
          visibility: visible !important;
        }

        .el-menu-item {
          height: 60px;
          line-height: 60px;
          border-bottom: 2px solid transparent;
          margin: 0 5px; // 减少间距以容纳更多菜单项
          padding: 0 15px; // 减少内边距
          font-size: 14px; // 稍微减小字体

          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
            border-bottom-color: #409EFF;
          }

          &.is-active {
            border-bottom-color: #409EFF;
            background-color: rgba(64, 158, 255, 0.1);
          }

          .el-icon {
            margin-right: 4px; // 减少图标和文字间距
          }

          span {
            font-size: 14px;
          }
        }
      }
    }

    .user-section {
      flex-shrink: 0; // 防止用户区域被压缩
      width: 200px; // 固定用户区域宽度
      display: flex;
      justify-content: flex-end;

      .user-dropdown {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 20px;
        transition: background-color 0.3s;

        &:hover {
          background-color: rgba(64, 158, 255, 0.1);
        }

        .user-avatar {
          margin-right: 8px;
        }

        .dropdown-icon {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f5f5f5;
  }
}

// 全局样式，确保Element Plus菜单不会自动隐藏项目
.nav-menu.el-menu--horizontal {
  width: 100% !important;
  overflow: visible !important;

  .el-menu-item {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
  }

  // 强制显示所有菜单项
  .el-menu-item:nth-child(1),
  .el-menu-item:nth-child(2),
  .el-menu-item:nth-child(3),
  .el-menu-item:nth-child(4),
  .el-menu-item:nth-child(5),
  .el-menu-item:nth-child(6) {
    display: inline-block !important;
    visibility: visible !important;
  }

  // 隐藏可能的更多按钮
  .el-menu-item.is-disabled,
  .el-sub-menu.is-disabled {
    display: none !important;
  }
}

/* 我的对战按钮样式 */
.my-battle-item {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 6px;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.my-battle-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.my-battle-item.battling {
  animation: pulse 2s infinite;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
}

.battle-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}
</style>
