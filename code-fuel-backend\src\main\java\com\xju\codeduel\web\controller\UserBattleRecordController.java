package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IUserBattleRecordService;
import com.xju.codeduel.model.domain.UserBattleRecord;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/userBattleRecord")
public class UserBattleRecordController {

    private final Logger logger = LoggerFactory.getLogger( UserBattleRecordController.class );

    @Autowired
    private IUserBattleRecordService userBattleRecordService;


    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<UserBattleRecord> getById(@PathVariable("id") Long id)throws Exception {
        UserBattleRecord userBattleRecord = userBattleRecordService.getById(id);
        return JsonResponse.success(userBattleRecord);
    }
}

