package com.xju.codeduel.web.controller;

import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.UserDTO;
import com.xju.codeduel.model.dto.UserRatingChangeDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.service.IUsersService;
import com.xju.codeduel.service.IVerificationService;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.PasswordUtils;
import com.xju.codeduel.common.utls.SessionUtils;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.dto.UserRegisterDTO;
import com.xju.codeduel.model.dto.CodeforcesUserInfo;
import com.xju.codeduel.model.dto.UserUpdateDTO;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/users")
public class UsersController {

    private final Logger logger = LoggerFactory.getLogger( UsersController.class );
    @Autowired
    private IUsersService usersService;

    @Autowired
    private IVerificationService verificationService;


    /**
     * 描述：根据Id 查询
     *
     */
    @RequestMapping(value = "/id/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Users> getById(@PathVariable("id") Long id)throws Exception {
        Users users = usersService.getById(id);
        System.out.println(users);
        return JsonResponse.success(users);
    }

    //登录
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<Users> login(@RequestBody Users users) {
        try {
            // 加密用户输入的密码
            if (users.getPassword() != null) {
                String encryptedPassword = PasswordUtils.simpleEncrypt(users.getPassword());
                users.setPassword(encryptedPassword);
            }

            Users users1 = usersService.login(users);
            if (users1 != null) {
                // 清除密码字段后返回
                users1.setPassword(null);
                logger.info("用户 {} 登录成功", users1.getCodeforcesId());
            } else {
                logger.warn("用户 {} 登录失败", users.getCodeforcesId());
            }
            return JsonResponse.success(users1);
        } catch (Exception e) {
            logger.error("登录过程发生错误: {}", e.getMessage(), e);
            return JsonResponse.failure("登录失败，请稍后重试");
        }
    }

    @RequestMapping("pageList")
    @ResponseBody
    public JsonResponse pageList(PageDTO pageDTO, UserDTO users) {
        Page<Users> page = usersService.pageList(pageDTO, users);
        return JsonResponse.success(page);
    }

    /**
     * 更新用户信息接口
     *
     * 功能说明：
     * 1. 更新数据库中的用户信息
     * 2. 如果更新的是当前登录用户，同时更新Session中的用户信息
     * 3. 这样确保前端通过getInfo接口获取的数据是最新的
     *
     * @param users 要更新的用户信息
     * @return 更新结果
     */
    @RequestMapping("update")
    @ResponseBody
    public JsonResponse update(@RequestBody Users users) {
        try {
            // 获取当前登录用户信息，用于判断是否需要更新Session
            Users currentUser = SessionUtils.getCurrentUserInfo();
            boolean isCurrentUser = currentUser != null && currentUser.getId().equals(users.getId());

            // 处理密码字段：如果有新密码则加密，否则不更新密码字段
            if (users.getPassword() != null && !users.getPassword().trim().isEmpty()) {
                String encryptedPassword = PasswordUtils.simpleEncrypt(users.getPassword());
                users.setPassword(encryptedPassword);
                logger.info("用户 {} 密码已更新", users.getCodeforcesId());
            } else {
                // 密码为空时设为null，MyBatis-Plus会忽略null字段，不会覆盖原密码
                users.setPassword(null);
                logger.info("用户 {} 更新信息，密码字段跳过", users.getCodeforcesId());
            }

            // 更新数据库中的用户信息
            boolean update = usersService.updateById(users);

            // 关键逻辑：如果更新的是当前登录用户，需要同步更新Session
            // 这样前端调用getInfo接口时就能获取到最新的数据（如头像等）
            if (update && isCurrentUser) {
                logger.info("正在同步更新Session中的用户信息，用户ID: {}", users.getId());

                // 从数据库重新查询最新的用户信息，确保数据完整性
                Users updatedUser = usersService.getById(users.getId());
                if (updatedUser != null) {
                    // 清除密码字段，避免在Session中存储敏感信息
                    updatedUser.setPassword(null);

                    // 更新Session中的用户信息
                    SessionUtils.saveCurrentUserInfo(updatedUser);
                    logger.info("✅ Session中的用户信息已同步更新，用户: {}", updatedUser.getCodeforcesId());
                } else {
                    logger.warn("⚠️ 无法从数据库获取更新后的用户信息，用户ID: {}", users.getId());
                }
            }

            return JsonResponse.success(update);
        } catch (Exception e) {
            logger.error("更新用户信息失败: {}", e.getMessage(), e);
            return JsonResponse.failure("更新用户信息失败，请稍后重试");
        }
    }

    /**
     * 获取用户列表（仅基本信息 + 对战记录条数，用于排行榜）
     */
    @GetMapping("list")
    @ResponseBody
    public JsonResponse getUsersList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(defaultValue = "") String username) {

        // 创建分页参数对象
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPageNo(page);
        pageDTO.setPageSize(size);

        // 处理username参数：如果为空字符串，则查询所有用户
        String searchUsername = (username == null || username.trim().isEmpty()) ? null : username.trim();

        Page<UserDTO> result = usersService.getUsersListWithBattleCount(pageDTO, searchUsername);

        return JsonResponse.success(result);
    }

    /**
     * 获取用户详细Rating历史记录
     */
    @GetMapping("rating-history/{username}")
    @ResponseBody
    public JsonResponse getUserRatingHistoryDetail(@PathVariable String username) {

        if (username == null || username.trim().isEmpty()) {
            return JsonResponse.failure("用户名不能为空");
        }

        UserRatingChangeDTO result = usersService.getUserRatingHistoryByUsername(username.trim());

        if (result == null) {
            return JsonResponse.failure("用户不存在");
        }

        return JsonResponse.success(result);
    }


    /**
     * 根据用户名获取用户信息
     */
    @RequestMapping(value = "/profile/{username}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Users> getUserByUsername(@PathVariable("username") String username) {
        Users user = usersService.getUserByUsername(username);
        return JsonResponse.success(user);
    }

    /**
     * 生成验证码
     * 用于Codeforces身份验证
     */
    @PostMapping("/generate-verification-code")
    @ResponseBody
    public JsonResponse<String> generateVerificationCode() {
        try {
            String verificationCode = verificationService.generateVerificationCode();
            logger.info("为用户生成验证码: {}", verificationCode);
            return JsonResponse.success(verificationCode);
        } catch (Exception e) {
            logger.error("生成验证码失败: {}", e.getMessage(), e);
            return JsonResponse.failure("生成验证码失败");
        }
    }

    /**
     * 获取Codeforces用户的firstName
     */
    @GetMapping("/get-codeforces-firstname/{codeforcesId}")
    @ResponseBody
    public JsonResponse<String> getCodeforcesFirstName(@PathVariable("codeforcesId") String codeforcesId) {
        try {
            if (codeforcesId == null || codeforcesId.trim().isEmpty()) {
                return JsonResponse.failure("Codeforces用户名不能为空");
            }

            String firstName = verificationService.getCodeforcesUserFirstName(codeforcesId.trim());

            if (firstName != null) {
                logger.info("成功获取用户 {} 的firstName: {}", codeforcesId, firstName);
                return JsonResponse.success(firstName);
            } else {
                logger.warn("获取用户 {} 的firstName失败", codeforcesId);
                return JsonResponse.failure("获取firstName失败，请检查用户名是否正确");
            }

        } catch (Exception e) {
            logger.error("获取Codeforces firstName失败: {}", e.getMessage(), e);
            return JsonResponse.failure("获取过程发生错误，请稍后重试");
        }
    }

    /**
     * 验证Codeforces用户身份并完成注册
     * 验证成功后直接将用户信息保存到数据库
     */
    @PostMapping("/verify-codeforces")
    @ResponseBody
    public JsonResponse<Users> verifyCodeforcesUser(@RequestBody UserRegisterDTO registerDTO) {
        try {
            // 参数验证
            if (registerDTO.getCodeforcesId() == null || registerDTO.getCodeforcesId().trim().isEmpty()) {
                return JsonResponse.failure("Codeforces用户名不能为空");
            }

            if (registerDTO.getVerificationString() == null || registerDTO.getVerificationString().trim().isEmpty()) {
                return JsonResponse.failure("验证字符串不能为空");
            }

            if (registerDTO.getPassword() == null || registerDTO.getPassword().trim().isEmpty()) {
                return JsonResponse.failure("密码不能为空");
            }

            String codeforcesId = registerDTO.getCodeforcesId().trim();
            String password = registerDTO.getPassword().trim();
            String verificationString = registerDTO.getVerificationString().trim();

            logger.info("🔍 开始验证并注册Codeforces用户: {}", codeforcesId);

            // 检查用户是否已存在
            Users existingUser = usersService.getUserByUsername(codeforcesId);
            if (existingUser != null) {
                logger.warn("用户 {} 已存在，无法重复注册", codeforcesId);
                return JsonResponse.failure("该Codeforces用户已注册，请直接登录");
            }

            // 获取Codeforces用户完整信息（包含头像等）
            logger.info("📡 获取用户 {} 的完整Codeforces信息", codeforcesId);
            CodeforcesUserInfo codeforcesUserInfo = verificationService.getCodeforcesUserInfo(codeforcesId);

            if (codeforcesUserInfo == null) {
                return JsonResponse.failure("无法获取Codeforces用户信息，请检查用户名是否正确");
            }

            // 验证firstName是否匹配验证字符串
            if (!verificationString.equals(codeforcesUserInfo.getFirstName())) {
                logger.warn("🔐 用户 {} 的firstName验证失败，期望: {}, 实际: {}",
                        codeforcesId, verificationString, codeforcesUserInfo.getFirstName());
                return JsonResponse.failure("Codeforces身份验证失败，请确保已将First name设置为验证字符串");
            }

            logger.info("✅ 用户 {} Codeforces身份验证成功，开始注册", codeforcesId);

            // 创建新用户对象
            Users newUser = new Users();

            // 设置基本信息
            newUser.setCodeforcesId(codeforcesId);

            // 加密密码并保存
            String encryptedPassword = PasswordUtils.simpleEncrypt(password);
            newUser.setPassword(encryptedPassword);
            logger.info("🔒 用户密码已加密保存");

            // 设置从Codeforces获取的信息
            newUser.setAvatar(codeforcesUserInfo.getAvatar() != null ? codeforcesUserInfo.getAvatar() : "");
            newUser.setRating(codeforcesUserInfo.getRating() != null ? codeforcesUserInfo.getRating() : 1500);
            logger.info("🖼️ 设置用户头像: {}", codeforcesUserInfo.getAvatar());
            logger.info("⭐ 设置用户Rating: {}", codeforcesUserInfo.getRating());

            // 设置系统默认值
            newUser.setStatus(0); // 正常状态
            newUser.setIsAdmin(0); // 非管理员
            newUser.setRegisterTime(java.time.LocalDateTime.now());
            newUser.setUpdateTime(java.time.LocalDateTime.now());

            // 保存用户到数据库
            logger.info("💾 开始保存用户 {} 到数据库", codeforcesId);
            boolean saved = usersService.save(newUser);

            if (saved) {
                logger.info("🎉 用户 {} 注册成功！用户ID: {}", codeforcesId, newUser.getId());
                logger.info("📊 用户信息 - Rating: {}, 头像: {}", newUser.getRating(), newUser.getAvatar());

                // 清除密码字段后返回给前端
                newUser.setPassword(null);
                return JsonResponse.success(newUser);
            } else {
                logger.error("❌ 用户 {} 注册失败：数据库保存失败", codeforcesId);
                return JsonResponse.failure("注册失败，数据库保存出错，请稍后重试");
            }

        } catch (Exception e) {
            logger.error("💥 验证并注册用户失败: {}", e.getMessage(), e);
            return JsonResponse.failure("注册过程发生错误，请稍后重试");
        }
    }



    /**
     * 获取首页统计数据
     */
    @GetMapping("/home-stats")
    @ResponseBody
    public JsonResponse getHomeStats() {
        try {
            Map<String, Object> stats = usersService.getHomeStats();
            return JsonResponse.success(stats);
        } catch (Exception e) {
            return JsonResponse.failure("获取统计数据失败: " + e.getMessage());
        }
    }

}

