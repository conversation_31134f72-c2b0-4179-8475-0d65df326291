package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IUserRatingHistoriesService;
import com.xju.codeduel.model.domain.UserRatingHistories;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/userRatingHistories")
public class UserRatingHistoriesController {

    private final Logger logger = LoggerFactory.getLogger( UserRatingHistoriesController.class );

    @Autowired
    private IUserRatingHistoriesService userRatingHistoriesService;


    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<UserRatingHistories> getById(@PathVariable("id") Long id)throws Exception {
        UserRatingHistories userRatingHistories = userRatingHistoriesService.getById(id);
        return JsonResponse.success(userRatingHistories);
    }
}

