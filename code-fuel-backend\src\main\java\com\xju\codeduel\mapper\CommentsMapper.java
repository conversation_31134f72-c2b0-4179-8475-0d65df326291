package com.xju.codeduel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Comments;
import com.xju.codeduel.model.dto.CommentDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 评论 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface CommentsMapper extends BaseMapper<Comments> {
    /**
     * 分页查询帖子下的评论信息
     * @param page 分页对象
     * @param postId 帖子ID
     * @return 评论DTO分页结果
     */
    Page<CommentDTO> getCommentsByPostId(Page<CommentDTO> page, @Param("postId") Integer postId);

}
