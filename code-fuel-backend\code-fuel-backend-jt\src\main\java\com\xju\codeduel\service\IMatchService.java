package com.xju.codeduel.service;

import java.util.Map;

/**
 * 匹配服务接口
 * 
 * 功能说明：
 * 1. 管理用户匹配队列
 * 2. 实现匹配算法
 * 3. 自动创建匹配房间
 * 4. 处理匹配超时
 * 
 * 匹配流程：
 * 1. 用户发起匹配请求
 * 2. 系统根据Rating和难度偏好寻找合适对手
 * 3. 找到对手后自动创建房间并开始对战
 * 4. 未找到对手则加入等待队列
 * 5. 支持匹配取消和超时处理
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IMatchService {

    /**
     * 开始匹配
     * 
     * 功能说明：
     * 1. 验证用户状态（不能重复匹配、不能在房间中）
     * 2. 根据用户Rating和难度偏好寻找合适对手
     * 3. 如果找到对手，创建匹配房间并开始对战
     * 4. 如果没有找到，将用户加入等待队列
     * 
     * 匹配算法：
     * - Rating差距在200分以内优先匹配
     * - 难度偏好有重叠的用户优先匹配
     * - 等待时间越长，匹配条件越宽松
     * 
     * @param userId 用户ID
     * @param minDifficulty 最小难度偏好
     * @param maxDifficulty 最大难度偏好
     * @return 匹配结果，包含状态、房间信息等
     * @throws RuntimeException 当用户状态不允许匹配时抛出异常
     */
    Map<String, Object> startMatch(Long userId, Integer minDifficulty, Integer maxDifficulty);

    /**
     * 取消匹配
     * 
     * 功能说明：
     * 1. 将用户从匹配队列中移除
     * 2. 清除相关的匹配状态
     * 3. 停止匹配超时计时器
     * 
     * @param userId 用户ID
     * @return 是否成功取消（false表示用户不在匹配队列中）
     */
    boolean cancelMatch(Long userId);

    /**
     * 获取匹配状态
     * 
     * 功能说明：
     * 1. 查询用户当前的匹配状态
     * 2. 返回等待时间、匹配进度等信息
     * 3. 如果匹配成功，返回房间信息
     * 
     * @param userId 用户ID
     * @return 匹配状态信息
     */
    Map<String, Object> getMatchStatus(Long userId);

    /**
     * 处理匹配超时
     * 
     * 功能说明：
     * 1. 定期检查匹配队列中的超时用户
     * 2. 自动取消超时的匹配请求
     * 3. 通知用户匹配超时
     * 
     * 注意：这个方法由定时任务调用
     */
    void handleMatchTimeout();

    /**
     * 清理匹配队列
     *
     * 功能说明：
     * 1. 清理无效的匹配请求
     * 2. 移除已离线的用户
     * 3. 优化匹配队列性能
     *
     * 注意：这个方法由定时任务调用
     */
    void cleanupMatchQueue();

    /**
     * 强制清理用户的匹配队列记录
     *
     * 功能说明：
     * 1. 无条件从匹配队列中移除指定用户
     * 2. 用于对战结束后的清理工作
     * 3. 不抛出异常，即使用户不在队列中
     *
     * @param userId 用户ID
     */
    void forceRemoveFromQueue(Long userId);
}
