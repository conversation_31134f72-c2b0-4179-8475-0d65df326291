<template>
  <div class="ranking-container">
    <!-- 排行榜标题和筛选 -->
    <el-card class="ranking-header">
      <div class="header-content">
        <h2>
          <el-icon><Trophy /></el-icon>
          排行榜
        </h2>
        <div class="filters">
          <el-input
              v-model="searchKeyword"
              placeholder="搜索用户..."
              style="width: 200px; margin-left: 10px"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button
              type="primary"
              @click="handleSearch"
              :loading="loading"
              style="margin-left: 10px"
          >
            搜索
          </el-button>
          <el-button
              type="success"
              @click="handleRefresh"
              :loading="loading"
              style="margin-left: 10px"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 前三名展示 -->
    <el-card class="top-three" shadow="hover">
      <div class="podium">
        <!-- 第二名 -->
        <div class="podium-item second">
          <div class="rank-badge silver">2</div>
          <el-avatar :size="80" :src="topUsers[1]?.avatar" />
          <div class="user-info">
            <div class="username">{{ topUsers[1]?.codeforcesId }}</div>
            <div class="rating">{{ topUsers[1]?.rating }}</div>
          </div>
        </div>

        <!-- 第一名 -->
        <div class="podium-item first">
          <div class="rank-badge gold">1</div>
          <el-avatar :size="100" :src="topUsers[0]?.avatar" />
          <div class="user-info">
            <div class="username">{{ topUsers[0]?.codeforcesId }}</div>
            <div class="rating">{{ topUsers[0]?.rating }}</div>
          </div>
          <el-icon class="crown"><Star /></el-icon>
        </div>

        <!-- 第三名 -->
        <div class="podium-item third">
          <div class="rank-badge bronze">3</div>
          <el-avatar :size="80" :src="topUsers[2]?.avatar" />
          <div class="user-info">
            <div class="username">{{ topUsers[2]?.codeforcesId }}</div>
            <div class="rating">{{ topUsers[2]?.rating }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 完整排行榜 -->
    <el-card class="ranking-table">
      <template #header>
        <div class="table-header">
          <span>完整排行榜</span>
        </div>
      </template>
      <el-table
          :data="rankingList"
          style="width: 100%"
          :row-class-name="getRowClassName"
          v-loading="loading"
      >
        <el-table-column prop="rank" label="排名" width="80" align="center">
          <template #default="{ row }">
            <div class="rank-cell">
              <span v-if="row.rank <= 3" class="medal">
                <el-icon v-if="row.rank === 1" class="gold-medal"><Trophy /></el-icon>
                <el-icon v-else-if="row.rank === 2" class="silver-medal"><Trophy /></el-icon>
                <el-icon v-else class="bronze-medal"><Trophy /></el-icon>
              </span>
              <span class="rank-number">{{ row.rank }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="用户" min-width="200">
          <template #default="{ row }">
            <div class="user-cell">
              <el-avatar :size="40" :src="row.avatar" />
              <div class="user-details">
                <div class="username">{{ row.codeforcesId }}</div>
                <div class="user-meta">
                  <span class="last-active">{{ formatLastActive(row.lastLogin) }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="rating" label="Rating" width="120" align="center">
          <template #default="{ row }">
            <div class="rating-cell">
              <span :class="getRatingClass(row.rating)">{{ row.rating }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="totalBattles" label="总对战数" width="100" align="center" />

        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button
                type="primary"
                size="small"
                @click="goToUserProfile(row)"
                :icon="TrendCharts"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalUsers"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 个人排名卡片 -->
    <el-card class="my-ranking" v-if="userRanking">
      <template #header>
        <span>我的排名</span>
      </template>
      <div class="my-rank-content">
        <div class="rank-info">
          <div class="my-rank">第 {{ userRanking.rank }} 名</div>
          <div class="my-rating">Rating: {{ userRanking.rating }}</div>
        </div>
        <div class="rank-progress">
          <div class="progress-item">
            <span>距离上一名还需：</span>
            <span class="rating-diff">{{ userRanking.toNextRank }} Rating</span>
          </div>
          <div class="progress-item">
            <span>本月变化：</span>
            <span :class="userRanking.monthlyChange >= 0 ? 'rating-up' : 'rating-down'">
              {{ userRanking.monthlyChange >= 0 ? '+' : '' }}{{ userRanking.monthlyChange }}
            </span>
          </div>
        </div>
      </div>
    </el-card>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Trophy,
  Search,
  Refresh,
  Star,
  TrendCharts
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getUserList, searchUser, getInfo, getUserDetailHistory } from '@/api/api.js'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)
const totalPages = ref(1)




// 前三名数据 - 使用缓存机制
const topUsers = ref([])
const cachedTopThree = ref([]) // 缓存的前3名，不随分页变化
const isFirstLoad = ref(true) // 标记是否为首次加载

// 排行榜数据
const rankingList = ref([])

// 用户个人排名
const userRanking = ref(null)
const currentUser = ref(null) // 当前登录用户信息



// 方法
const getRowClassName = ({ row }) => {
  if (row.rank <= 3) return 'top-three-row'
  if (row.rank <= 10) return 'top-ten-row'
  return ''
}

const getRatingClass = (rating) => {
  if (rating >= 3000) return 'rating-legendary'
  if (rating >= 2400) return 'rating-grandmaster'
  if (rating >= 2100) return 'rating-master'
  if (rating >= 1900) return 'rating-expert'
  if (rating >= 1600) return 'rating-specialist'
  return 'rating-pupil'
}



const formatLastActive = (lastLogin) => {
  const now = new Date()
  const diff = now - new Date(lastLogin)
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天活跃'
  if (days === 1) return '昨天活跃'
  if (days <= 7) return `${days}天前活跃`
  return '一周前活跃'
}

// 刷新数据 - 跳转到第一页并更新缓存
const handleRefresh = async () => {
  console.log('点击刷新按钮')

  // 重置搜索状态
  searchKeyword.value = ''

  // 跳转到第一页
  currentPage.value = 1

  // 标记为需要更新缓存
  isFirstLoad.value = true

  // 重新加载数据
  await loadRankingData()

  // 更新用户排名信息
  await loadUserRanking()

  // ElMessage.success('排行榜已刷新，前3名和用户信息已更新')
}

// 搜索用户
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索关键词为空，重新加载所有数据
    currentPage.value = 1
    await loadRankingData()
    return
  }

  loading.value = true
  try {
    console.log('搜索用户:', searchKeyword.value)

    const params = {
      username: searchKeyword.value.trim(),
      page: currentPage.value,
      size: pageSize.value
    }
    const response = await searchUser(params)

    if (response.status && response.data && response.data.records) {
      const data = response.data
      const users = processUserList(data.records)

      rankingList.value = users

      // 搜索时保持使用缓存的前3名，不更新
      topUsers.value = cachedTopThree.value

      // 更新分页信息
      totalUsers.value = data.total
      currentPage.value = data.current
      totalPages.value = data.pages

      console.log(`搜索到 ${users.length} 个用户`)
      console.log('搜索时保持前3名不变:', topUsers.value)

      if (users.length > 0) {
        // ElMessage.success(`找到 ${data.total} 个相关用户`)
      } else {
        ElMessage.info('未找到相关用户')
      }
    } else {
      console.error('搜索API返回错误:', response)
      ElMessage.error('搜索失败')
      rankingList.value = []
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败: ' + error.message)
    rankingList.value = []
  } finally {
    loading.value = false
  }
}


const handleSizeChange = async (val) => {
  pageSize.value = val
  currentPage.value = 1 // 切换每页数量时重置页码为 1

  // 重新加载数据
  if (searchKeyword.value.trim()) {
    await handleSearch()
  } else {
    await loadRankingData()
  }
}

const handleCurrentChange = async (val) => {
  currentPage.value = val

  // 重新加载数据
  if (searchKeyword.value.trim()) {
    await handleSearch()
  } else {
    await loadRankingData()
  }
}



// 跳转到用户个人中心
const goToUserProfile = (user) => {
  console.log('跳转到用户个人中心:', user.codeforcesId)
  router.push(`/dashboard/profile/${user.codeforcesId}`)
}





// 数据处理函数：处理用户列表数据
const processUserList = (users) => {
  // 直接处理Users对象数组，添加排名和在线状态
  // 计算正确的排名：(当前页-1) * 每页数量 + 当前索引 + 1
  const startRank = (currentPage.value - 1) * pageSize.value

  return users.map((user, index) => ({
    ...user,
    rank: startRank + index + 1,
    totalBattles: user.totalBattles || 0,
    isOnline: Math.random() > 0.5,
    lastLogin: user.lastLogin || new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  }))
}

// 加载排行榜数据（分页）
const loadRankingData = async () => {
  loading.value = true
  try {
    console.log('开始加载用户列表数据...')

    // 使用新的用户列表API
    const params = {
      username: '',
      page: currentPage.value,
      size: pageSize.value
    }
    const response = await getUserList(params)

    console.log('获取到的API响应:', response)
    console.log('response.status:', response.status)
    console.log('response.data:', response.data)
    console.log('response.data.records:', response.data?.records)

    if (response.status && response.data && response.data.records) {
      const data = response.data
      console.log('开始处理用户数据，records长度:', data.records.length)

      const users = processUserList(data.records)
      console.log('处理后的用户数据:', users)

      rankingList.value = users

      // 缓存逻辑：只在第一页或刷新时更新前3名
      if (currentPage.value === 1 || isFirstLoad.value) {
        cachedTopThree.value = users.slice(0, 3)
        console.log('更新缓存的前3名:', cachedTopThree.value)
        isFirstLoad.value = false
      }

      // 始终使用缓存的前3名
      topUsers.value = cachedTopThree.value

      // 更新分页信息
      totalUsers.value = data.total
      currentPage.value = data.current
      totalPages.value = data.pages

      console.log('处理后的排行榜数据:', users)
      // console.log(`成功加载第${data.current}页，共${data.total}个用户`)
      console.log('当前显示的前3名:', topUsers.value)

      if (users.length > 0) {
        // ElMessage.success(`成功加载第${data.current}页用户数据`)
      } else {
        ElMessage.warning('当前页面没有用户数据')
      }
    } else {
      console.error('API返回错误或数据格式不正确:')
      console.error('- response.status:', response.status)
      console.error('- response.data:', response.data)
      console.error('- response.data.records:', response.data?.records)
      ElMessage.error('API返回数据格式错误')
      rankingList.value = []
    }
  } catch (error) {
    console.error('加载排行榜数据失败:', error)
    ElMessage.error('加载排行榜数据失败: ' + error.message)
    rankingList.value = []
  } finally {
    loading.value = false
  }
}

// 获取当前用户排名信息
const loadUserRanking = async () => {
  try {
    // 获取当前用户信息
    const userInfo = await getInfo()
    console.log('当前用户信息API响应:', userInfo)

    if (userInfo.status && userInfo.data && userInfo.data.codeforcesId) {
      currentUser.value = userInfo.data
      console.log('当前登录用户:', currentUser.value.codeforcesId)

      // 计算用户排名
      await calculateUserRankFromFullList()
    } else {
      console.log('用户未登录，不显示个人排名')
      currentUser.value = null
      userRanking.value = null
    }
  } catch (error) {
    console.error('获取用户排名失败:', error)
    userRanking.value = null
  }
}

// 从完整排行榜计算用户排名
const calculateUserRankFromFullList = async () => {
  try {
    // 获取足够多的数据来计算准确排名
    const params = {
      username: '',
      page: 1,
      size: 100 // 获取前100名来计算
    }
    const response = await getUserList(params)

    if (response.status && response.data && response.data.records) {
      const allUsers = response.data.records
      const userIndex = allUsers.findIndex(user =>
          user.codeforcesId === currentUser.value.codeforcesId
      )

      if (userIndex !== -1) {
        const user = allUsers[userIndex]
        const nextUser = allUsers[userIndex - 1] // 排名更高的用户

        // 获取用户历史数据来计算本月变化
        const monthlyChange = await calculateMonthlyChange(user.codeforcesId)

        userRanking.value = {
          rank: userIndex + 1,
          rating: user.rating,
          toNextRank: nextUser ? Math.max(0, nextUser.rating - user.rating + 1) : 0,
          monthlyChange: monthlyChange
        }

        console.log('用户排名信息:', userRanking.value)
      } else {
        // 用户不在前100名，可能排名更低
        userRanking.value = {
          rank: 100, // 显示100+
          rating: currentUser.value.rating || 1500,
          toNextRank: 0,
          monthlyChange: 0
        }
      }
    }
  } catch (error) {
    console.error('计算用户排名失败:', error)
  }
}

// 计算本月Rating变化
const calculateMonthlyChange = async (username) => {
  try {
    console.log('计算用户本月变化:', username)
    const response = await getUserDetailHistory(username)
    console.log('用户历史数据响应:', response)

    if (response.status && response.data && response.data.ratingHistories) {
      const histories = response.data.ratingHistories
      console.log(`用户 ${username} 的历史记录数量:`, histories.length)

      if (histories.length === 0) {
        console.log('用户无历史记录')
        return 0
      }

      // 获取当前月份的第一天
      const now = new Date()
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      console.log('本月开始时间:', monthStart)

      // 找到本月的记录
      const thisMonthRecords = histories.filter(record => {
        const recordTime = new Date(record.recordTime)
        return recordTime >= monthStart
      })

      console.log(`用户 ${username} 本月记录数量:`, thisMonthRecords.length)

      if (thisMonthRecords.length === 0) {
        console.log('用户本月无记录')
        return 0
      }

      // 按时间排序
      thisMonthRecords.sort((a, b) => new Date(a.recordTime) - new Date(b.recordTime))

      const firstThisMonth = thisMonthRecords[0]
      const lastThisMonth = thisMonthRecords[thisMonthRecords.length - 1]

      const monthlyChange = lastThisMonth.newRating - firstThisMonth.oldRating
      console.log(`用户 ${username} 本月变化: ${firstThisMonth.oldRating} -> ${lastThisMonth.newRating} = ${monthlyChange}`)

      return monthlyChange
    } else {
      console.log('API响应格式错误或无数据')
    }
  } catch (error) {
    console.error('计算本月变化失败:', error)
  }
  return 0
}

onMounted(async () => {
  await loadRankingData()
  await loadUserRanking()
})
</script>

<style lang="scss" scoped>
.ranking-container {
  max-width: 1200px;
  margin: 0 auto;

  .ranking-header {
    margin-bottom: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        display: flex;
        align-items: center;
        margin: 0;

        .el-icon {
          margin-right: 10px;
          color: #FFD700;
        }
      }

      .filters {
        display: flex;
        align-items: center;
      }
    }
  }

  .top-three {
    margin-bottom: 20px;

    .podium {
      display: flex;
      justify-content: center;
      align-items: end;
      padding: 20px 0;

      .podium-item {
        position: relative;
        text-align: center;
        margin: 0 30px;

        &.first {
          order: 2;

          .crown {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 2em;
            color: #FFD700;
          }
        }

        &.second {
          order: 1;
        }

        &.third {
          order: 3;
        }

        .rank-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          color: white;

          &.gold {
            background: linear-gradient(45deg, #FFD700, #FFA500);
          }

          &.silver {
            background: linear-gradient(45deg, #C0C0C0, #A0A0A0);
          }

          &.bronze {
            background: linear-gradient(45deg, #CD7F32, #B8860B);
          }
        }

        .user-info {
          margin-top: 10px;

          .username {
            font-weight: bold;
            font-size: 1.1em;
          }

          .rating {
            color: #409EFF;
            font-weight: bold;
          }
        }
      }
    }
  }

  .ranking-table {
    margin-bottom: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .rank-cell {
      display: flex;
      align-items: center;
      justify-content: center;

      .medal {
        margin-right: 5px;

        .gold-medal {
          color: #FFD700;
        }

        .silver-medal {
          color: #C0C0C0;
        }

        .bronze-medal {
          color: #CD7F32;
        }
      }

      .rank-number {
        font-weight: bold;
      }
    }

    .user-cell {
      display: flex;
      align-items: center;

      .user-details {
        margin-left: 10px;

        .username {
          font-weight: bold;
        }

        .user-meta {
          display: flex;
          align-items: center;
          gap: 10px;

          .last-active {
            font-size: 0.8em;
            color: #666;
          }
        }
      }
    }

    .rating-cell {
      font-weight: bold;
    }

    .rating-change {
      .rating-up {
        color: #67C23A;
      }

      .rating-down {
        color: #F56C6C;
      }

      .rating-same {
        color: #909399;
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .my-ranking {
    .my-rank-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .rank-info {
        .my-rank {
          font-size: 1.5em;
          font-weight: bold;
          color: #409EFF;
        }

        .my-rating {
          color: #666;
          margin-top: 5px;
        }
      }

      .rank-progress {
        text-align: right;

        .progress-item {
          margin: 5px 0;

          .rating-diff {
            color: #409EFF;
            font-weight: bold;
          }

          .rating-up {
            color: #67C23A;
          }

          .rating-down {
            color: #F56C6C;
          }
        }
      }
    }
  }
}

// Rating颜色类
.rating-legendary {
  color: #FF0000;
  font-weight: bold;
}

.rating-grandmaster {
  color: #FF8C00;
  font-weight: bold;
}

.rating-master {
  color: #FFFF00;
  font-weight: bold;
}

.rating-expert {
  color: #0000FF;
  font-weight: bold;
}

.rating-specialist {
  color: #00FFFF;
  font-weight: bold;
}

.rating-pupil {
  color: #008000;
}

// 胜率颜色类
.winrate-excellent {
  color: #67C23A;
  font-weight: bold;
}

.winrate-good {
  color: #409EFF;
}

.winrate-average {
  color: #E6A23C;
}

.winrate-poor {
  color: #F56C6C;
}

// 表格行样式
:deep(.top-three-row) {
  background-color: #fff7e6;
}

:deep(.top-ten-row) {
  background-color: #f0f9ff;
}


</style>
