package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.PythonServiceUtils;
import com.xju.codeduel.service.IProblemsService;
import com.xju.codeduel.service.ITagsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 后台管理控制器
 * 
 * 功能说明：
 * 1. 管理题目和标签数据的更新
 * 2. 通过Python服务从Codeforces获取数据
 * 3. 提供数据更新状态查询
 * 
 * API接口：
 * - POST /api/admin/update-problems - 更新题目数据
 * - POST /api/admin/update-tags - 更新标签数据
 * - GET /api/admin/data-status - 获取数据更新状态
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/api/admin")
@Api(tags = "后台管理", description = "数据管理和系统维护功能")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private IProblemsService problemsService;

    @Autowired
    private ITagsService tagsService;

    @Autowired
    private PythonServiceUtils pythonServiceUtils;

    /**
     * 从Codeforces更新题目数据
     * 
     * 接口说明：
     * 1. 调用Python服务从Codeforces API获取题目数据
     * 2. 解析并保存到本地数据库
     * 3. 支持配置难度范围和获取页数
     * 
     * @param requestData 更新配置，包含maxPages、minRating、maxRating等
     * @return 更新结果
     */
    @PostMapping("/update-problems")
    @ApiOperation(value = "更新题目数据", notes = "从Codeforces获取最新题目数据并保存到数据库")
    public JsonResponse<Map<String, Object>> updateProblemsFromCodeforces(
            @ApiParam(value = "更新配置", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        Integer maxPages = requestData.get("maxPages") != null ? 
                          Integer.valueOf(requestData.get("maxPages").toString()) : 10;
        Integer minRating = requestData.get("minRating") != null ? 
                           Integer.valueOf(requestData.get("minRating").toString()) : 800;
        Integer maxRating = requestData.get("maxRating") != null ? 
                           Integer.valueOf(requestData.get("maxRating").toString()) : 3500;
        
        logger.info("🔄 开始更新题目数据，配置: maxPages={}, minRating={}, maxRating={}", 
                   maxPages, minRating, maxRating);
        
        try {
            // 检查Python服务是否可用
            if (!pythonServiceUtils.isServiceAvailable()) {
                return JsonResponse.failure("Python服务不可用，请检查服务状态");
            }
            
            // 调用Python服务更新题目数据
            String pythonServiceUrl = "http://localhost:5000/api/codeforces/problems/update";

            // 构建请求数据
            Map<String, Object> pythonRequestData = Map.of(
                "maxPages", maxPages,
                "minRating", minRating,
                "maxRating", maxRating
            );

            // 发送HTTP请求到Python服务
            java.net.http.HttpClient httpClient = java.net.http.HttpClient.newHttpClient();
            java.net.http.HttpRequest httpRequest = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create(pythonServiceUrl))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(
                        new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(pythonRequestData)))
                    .timeout(java.time.Duration.ofSeconds(60))
                    .build();

            java.net.http.HttpResponse<String> pythonResponse = httpClient.send(httpRequest,
                java.net.http.HttpResponse.BodyHandlers.ofString());

            if (pythonResponse.statusCode() != 200) {
                throw new RuntimeException("Python服务调用失败，状态码: " + pythonResponse.statusCode());
            }
            
            // 获取当前题目总数
            long totalCount = problemsService.count();
            
            Map<String, Object> result = Map.of(
                "totalCount", totalCount,
                "updateTime", System.currentTimeMillis(),
                "config", Map.of(
                    "maxPages", maxPages,
                    "minRating", minRating,
                    "maxRating", maxRating
                )
            );
            
            logger.info("✅ 题目数据更新完成，总数: {}", totalCount);
            return JsonResponse.success(result);
            
        } catch (Exception e) {
            logger.error("❌ 更新题目数据失败: {}", e.getMessage(), e);
            return JsonResponse.failure("更新题目数据失败: " + e.getMessage());
        }
    }

    /**
     * 从Codeforces更新标签数据
     * 
     * 接口说明：
     * 1. 调用Python服务从Codeforces API获取标签数据
     * 2. 解析并保存到本地数据库
     * 3. 支持强制更新配置
     * 
     * @param requestData 更新配置，包含forceUpdate等
     * @return 更新结果
     */
    @PostMapping("/update-tags")
    @ApiOperation(value = "更新标签数据", notes = "从Codeforces获取最新标签数据并保存到数据库")
    public JsonResponse<Map<String, Object>> updateTagsFromCodeforces(
            @ApiParam(value = "更新配置", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        Boolean forceUpdate = requestData.get("forceUpdate") != null ? 
                             Boolean.valueOf(requestData.get("forceUpdate").toString()) : false;
        
        logger.info("🔄 开始更新标签数据，强制更新: {}", forceUpdate);
        
        try {
            // 检查Python服务是否可用
            if (!pythonServiceUtils.isServiceAvailable()) {
                return JsonResponse.failure("Python服务不可用，请检查服务状态");
            }
            
            // 调用Python服务更新标签数据
            String pythonServiceUrl = "http://localhost:5000/api/codeforces/tags/update";

            // 构建请求数据
            Map<String, Object> pythonRequestData = Map.of(
                "forceUpdate", forceUpdate
            );

            // 发送HTTP请求到Python服务
            java.net.http.HttpClient httpClient = java.net.http.HttpClient.newHttpClient();
            java.net.http.HttpRequest httpRequest = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create(pythonServiceUrl))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(
                        new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(pythonRequestData)))
                    .timeout(java.time.Duration.ofSeconds(60))
                    .build();

            java.net.http.HttpResponse<String> pythonResponse = httpClient.send(httpRequest,
                java.net.http.HttpResponse.BodyHandlers.ofString());

            if (pythonResponse.statusCode() != 200) {
                throw new RuntimeException("Python服务调用失败，状态码: " + pythonResponse.statusCode());
            }
            
            // 获取当前标签总数
            long totalCount = tagsService.count();
            
            Map<String, Object> result = Map.of(
                "totalCount", totalCount,
                "updateTime", System.currentTimeMillis(),
                "config", Map.of(
                    "forceUpdate", forceUpdate
                )
            );
            
            logger.info("✅ 标签数据更新完成，总数: {}", totalCount);
            return JsonResponse.success(result);
            
        } catch (Exception e) {
            logger.error("❌ 更新标签数据失败: {}", e.getMessage(), e);
            return JsonResponse.failure("更新标签数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据更新状态
     * 
     * 接口说明：
     * 1. 查询题目和标签的数据统计
     * 2. 返回最后更新时间等信息
     * 3. 用于前端显示数据状态
     * 
     * @return 数据状态信息
     */
    @GetMapping("/data-status")
    @ApiOperation(value = "获取数据状态", notes = "查询题目和标签的数据统计信息")
    public JsonResponse<Map<String, Object>> getDataUpdateStatus() {
        
        logger.debug("📊 查询数据更新状态");
        
        try {
            // 获取题目数据统计
            long problemsCount = problemsService.count();
            
            // 获取标签数据统计
            long tagsCount = tagsService.count();
            
            // 构建状态数据
            Map<String, Object> statusData = new HashMap<>();

            Map<String, Object> problemsData = new HashMap<>();
            problemsData.put("totalCount", problemsCount);
            problemsData.put("lastUpdate", "暂无数据"); // TODO: 从配置或数据库获取最后更新时间
            problemsData.put("status", "idle");

            Map<String, Object> tagsData = new HashMap<>();
            tagsData.put("totalCount", tagsCount);
            tagsData.put("lastUpdate", "暂无数据"); // TODO: 从配置或数据库获取最后更新时间
            tagsData.put("status", "idle");

            statusData.put("problems", problemsData);
            statusData.put("tags", tagsData);
            statusData.put("pythonServiceStatus", pythonServiceUtils.isServiceAvailable());
            
            return JsonResponse.success(statusData);
            
        } catch (Exception e) {
            logger.error("❌ 获取数据状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("获取数据状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查Python服务状态
     * 
     * @return Python服务状态
     */
    @GetMapping("/python-service/status")
    @ApiOperation(value = "检查Python服务状态", notes = "检查Python服务是否可用")
    public JsonResponse<Map<String, Object>> checkPythonServiceStatus() {
        
        try {
            boolean isAvailable = pythonServiceUtils.isServiceAvailable();
            
            Map<String, Object> statusData = Map.of(
                "isAvailable", isAvailable,
                "checkTime", System.currentTimeMillis(),
                "serviceUrl", "http://localhost:5000" // Python服务地址
            );
            
            return JsonResponse.success(statusData);
            
        } catch (Exception e) {
            logger.error("❌ 检查Python服务状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("检查Python服务状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理缓存
     * 
     * @return 清理结果
     */
    @PostMapping("/clear-cache")
    @ApiOperation(value = "清理缓存", notes = "清理系统缓存数据")
    public JsonResponse<String> clearCache() {
        
        logger.info("🧹 开始清理系统缓存");
        
        try {
            // TODO: 实现缓存清理逻辑
            // 例如：清理房间缓存、用户缓存等
            
            logger.info("✅ 系统缓存清理完成");
            return JsonResponse.success("缓存清理完成");
            
        } catch (Exception e) {
            logger.error("❌ 清理缓存失败: {}", e.getMessage(), e);
            return JsonResponse.failure("清理缓存失败: " + e.getMessage());
        }
    }
}
