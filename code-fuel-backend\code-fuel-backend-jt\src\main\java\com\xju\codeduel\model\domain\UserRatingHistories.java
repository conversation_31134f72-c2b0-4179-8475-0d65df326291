package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户Rating历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_rating_histories")
@ApiModel(value="UserRatingHistories对象", description="用户Rating历史表")
public class UserRatingHistories implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

        @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

        @ApiModelProperty(value = "对战记录ID")
    @TableField("battle_id")
    private Long battleId;

        @ApiModelProperty(value = "变化前Rating")
    @TableField("old_rating")
    private Integer oldRating;

        @ApiModelProperty(value = "变化后Rating")
    @TableField("new_rating")
    private Integer newRating;

        @ApiModelProperty(value = "变化原因")
    @TableField("reason")
    private String reason;

        @ApiModelProperty(value = "记录时间")
    @TableField("record_time")
    private LocalDateTime recordTime;


}
