<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Avatar, Trophy, Clock } from '@element-plus/icons-vue'
import { createRoom, joinRoom, getRoomInfo, getActiveRooms, getAllTags } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'

// ==================== 组件属性和事件 ====================

/**
 * 组件事件定义
 * - room-created: 房间创建成功时触发
 * - room-joined: 成功加入房间时触发
 * - room-error: 房间操作出错时触发
 */
const emit = defineEmits(['room-created', 'room-joined', 'room-error'])

// ==================== 响应式数据 ====================

const userInfoStore = useUserInfoStore()

// 弹窗显示状态
const createDialogVisible = ref(false)
const joinDialogVisible = ref(false)
const roomListDialogVisible = ref(false)

// 创建房间表单数据
const createForm = ref({
  problemId: null,
  description: '',
  minDifficulty: 800,      // 最小难度分数
  maxDifficulty: 3500,     // 最大难度分数
  excludedTags: []         // 排除的标签ID列表
})

// 题目选择模式
const problemSelectionMode = ref('random')

// 所有标签列表
const allTags = ref([])

// 难度分数选项
const difficultyOptions = [
  { label: '800 (入门)', value: 800 },
  { label: '900', value: 900 },
  { label: '1000', value: 1000 },
  { label: '1100', value: 1100 },
  { label: '1200', value: 1200 },
  { label: '1300', value: 1300 },
  { label: '1400', value: 1400 },
  { label: '1500', value: 1500 },
  { label: '1600', value: 1600 },
  { label: '1700', value: 1700 },
  { label: '1800', value: 1800 },
  { label: '1900', value: 1900 },
  { label: '2000', value: 2000 },
  { label: '2100', value: 2100 },
  { label: '2200', value: 2200 },
  { label: '2300', value: 2300 },
  { label: '2400', value: 2400 },
  { label: '2500', value: 2500 },
  { label: '2600', value: 2600 },
  { label: '2700', value: 2700 },
  { label: '2800', value: 2800 },
  { label: '2900', value: 2900 },
  { label: '3000', value: 3000 },
  { label: '3100', value: 3100 },
  { label: '3200', value: 3200 },
  { label: '3300', value: 3300 },
  { label: '3400', value: 3400 },
  { label: '3500+', value: 3500 }
]

// 最大难度选项（基于最小难度动态计算）
const maxDifficultyOptions = computed(() => {
  return difficultyOptions.filter(option => option.value >= createForm.value.minDifficulty)
})

// 加入房间表单数据
const joinForm = ref({
  roomCode: ''
})

// 活跃房间列表
const activeRooms = ref([])

// 加载状态
const creating = ref(false)
const joining = ref(false)
const loadingRooms = ref(false)

// 当前用户信息
const currentUser = computed(() => userInfoStore.userInfo)

// ==================== 核心业务方法 ====================

/**
 * 加载所有标签
 *
 * 功能说明：
 * 1. 从后端获取所有可用的标签
 * 2. 用于创建房间时的标签排除选择
 */
const loadAllTags = async () => {
  try {
    const response = await getAllTags()

    if (response.status && response.data) {
      allTags.value = response.data
      console.log('📋 加载标签成功:', allTags.value)
    } else {
      console.error('❌ 加载标签失败:', response.message)
    }

  } catch (error) {
    console.error('❌ 加载标签失败:', error)
  }
}

/**
 * 创建房间
 *
 * 功能流程：
 * 1. 验证用户登录状态和表单数据
 * 2. 调用后端API创建房间
 * 3. 处理创建结果并触发相应事件
 * 4. 关闭弹窗并重置表单
 */
const handleCreateRoom = async () => {
  // 验证用户登录状态
  if (!currentUser.value || !currentUser.value.id) {
    ElMessage.error('请先登录')
    return
  }

  creating.value = true
  
  try {
    // 构建创建房间请求数据
    const createData = {
      creatorId: currentUser.value.id,
      creatorName: currentUser.value.codeforcesId,
      problemId: createForm.value.problemId || null,
      description: createForm.value.description || '欢迎来到我的房间',
      minDifficulty: createForm.value.minDifficulty,
      maxDifficulty: createForm.value.maxDifficulty,
      excludedTags: createForm.value.excludedTags
    }

    console.log('🏠 创建房间请求数据:', createData)

    // 调用API创建房间
    const response = await createRoom(createData)
    
    if (response.status) {
      ElMessage.success(`房间创建成功！房间码: ${response.data.roomCode}`)
      
      // 触发房间创建成功事件
      emit('room-created', response.data)
      
      // 关闭弹窗并重置表单
      createDialogVisible.value = false
      resetCreateForm()
      
    } else {
      ElMessage.error(response.message || '创建房间失败')
      emit('room-error', response.message)
    }
    
  } catch (error) {
    console.error('❌ 创建房间失败:', error)
    ElMessage.error('创建房间失败: ' + error.message)
    emit('room-error', error.message)
  } finally {
    creating.value = false
  }
}

/**
 * 加入房间
 * 
 * 功能流程：
 * 1. 验证房间码格式和用户登录状态
 * 2. 调用后端API加入房间
 * 3. 处理加入结果并触发相应事件
 * 4. 关闭弹窗并重置表单
 */
const handleJoinRoom = async () => {
  // 验证房间码
  if (!joinForm.value.roomCode || joinForm.value.roomCode.trim() === '') {
    ElMessage.warning('请输入房间码')
    return
  }

  // 验证房间码格式（6位数字）
  const roomCode = parseInt(joinForm.value.roomCode.trim())
  if (isNaN(roomCode) || roomCode < 100000 || roomCode > 999999) {
    ElMessage.warning('房间码格式不正确，请输入6位数字')
    return
  }

  // 验证用户登录状态
  if (!currentUser.value || !currentUser.value.id) {
    ElMessage.error('请先登录')
    return
  }

  joining.value = true
  
  try {
    // 构建加入房间请求数据
    const joinData = {
      roomCode: roomCode,
      userId: currentUser.value.id,
      userName: currentUser.value.codeforcesId
    }

    console.log('🚪 加入房间请求数据:', joinData)

    // 调用API加入房间
    const response = await joinRoom(joinData)
    
    if (response.status) {
      ElMessage.success(`成功加入房间 ${roomCode}`)
      
      // 触发加入房间成功事件
      emit('room-joined', response.data)
      
      // 关闭弹窗并重置表单
      joinDialogVisible.value = false
      resetJoinForm()
      
    } else {
      ElMessage.error(response.message || '加入房间失败')
      emit('room-error', response.message)
    }
    
  } catch (error) {
    console.error('❌ 加入房间失败:', error)
    ElMessage.error('加入房间失败: ' + error.message)
    emit('room-error', error.message)
  } finally {
    joining.value = false
  }
}

/**
 * 加载活跃房间列表
 * 
 * 功能说明：
 * 1. 从后端获取当前所有活跃房间
 * 2. 显示房间基本信息供用户选择
 * 3. 支持直接点击加入房间
 */
const loadActiveRooms = async () => {
  loadingRooms.value = true
  
  try {
    const response = await getActiveRooms()
    
    if (response.status) {
      activeRooms.value = response.data || []
      console.log('📋 活跃房间列表:', activeRooms.value)
    } else {
      ElMessage.error('获取房间列表失败')
      activeRooms.value = []
    }
    
  } catch (error) {
    console.error('❌ 获取房间列表失败:', error)
    ElMessage.error('获取房间列表失败: ' + error.message)
    activeRooms.value = []
  } finally {
    loadingRooms.value = false
  }
}

/**
 * 快速加入房间（从房间列表）
 * 
 * @param {Object} room 房间信息对象
 */
const quickJoinRoom = async (room) => {
  // 验证用户登录状态
  if (!currentUser.value || !currentUser.value.id) {
    ElMessage.error('请先登录')
    return
  }

  // 检查是否已在房间中
  const isAlreadyInRoom = room.participants.some(p => p.id === currentUser.value.id)
  if (isAlreadyInRoom) {
    ElMessage.warning('您已在该房间中')
    return
  }

  // 检查房间是否已满
  if (room.participantCount >= room.maxParticipants) {
    ElMessage.warning('房间已满')
    return
  }

  try {
    // 构建加入房间请求数据
    const joinData = {
      roomCode: room.roomCode,
      userId: currentUser.value.id,
      userName: currentUser.value.codeforcesId
    }

    // 调用API加入房间
    const response = await joinRoom(joinData)
    
    if (response.status) {
      ElMessage.success(`成功加入房间 ${room.roomCode}`)
      
      // 触发加入房间成功事件
      emit('room-joined', response.data)
      
      // 关闭房间列表弹窗
      roomListDialogVisible.value = false
      
    } else {
      ElMessage.error(response.message || '加入房间失败')
    }
    
  } catch (error) {
    console.error('❌ 快速加入房间失败:', error)
    ElMessage.error('加入房间失败: ' + error.message)
  }
}

// ==================== 辅助方法 ====================

/**
 * 重置创建房间表单
 */
const resetCreateForm = () => {
  createForm.value = {
    problemId: null,
    description: '',
    minDifficulty: 800,
    maxDifficulty: 3500,
    excludedTags: []
  }
}

/**
 * 重置加入房间表单
 */
const resetJoinForm = () => {
  joinForm.value = {
    roomCode: ''
  }
}

/**
 * 最小难度变化处理
 */
const onMinDifficultyChange = (value) => {
  // 如果最大难度小于最小难度，自动调整最大难度
  if (createForm.value.maxDifficulty < value) {
    createForm.value.maxDifficulty = value
  }
}

/**
 * 题目选择模式变化处理
 */
const onProblemSelectionModeChange = (mode) => {
  if (mode === 'random') {
    // 切换到随机模式时，清空手动输入的题目ID
    createForm.value.problemId = null
  }
}

/**
 * 标签选择变化处理
 */
const onTagSelectionChange = (selectedTags) => {
  console.log('🏷️ 标签选择变化:', selectedTags)
  console.log('🏷️ 当前表单数据:', createForm.value.excludedTags)
}

/**
 * 打开创建房间弹窗
 */
const openCreateDialog = () => {
  resetCreateForm()
  loadAllTags() // 加载标签数据
  createDialogVisible.value = true
}

/**
 * 打开加入房间弹窗
 */
const openJoinDialog = () => {
  resetJoinForm()
  joinDialogVisible.value = true
}

/**
 * 打开房间列表弹窗
 */
const openRoomListDialog = () => {
  roomListDialogVisible.value = true
  loadActiveRooms()
}

/**
 * 格式化房间状态显示
 * 
 * @param {string} status 房间状态
 * @returns {string} 格式化后的状态文本
 */
const formatRoomStatus = (status) => {
  const statusMap = {
    'WAITING': '等待中',
    'READY': '准备就绪',
    'BATTLING': '对战中',
    'FINISHED': '已结束'
  }
  return statusMap[status] || status
}

/**
 * 获取房间状态对应的标签类型
 * 
 * @param {string} status 房间状态
 * @returns {string} Element Plus标签类型
 */
const getRoomStatusType = (status) => {
  const typeMap = {
    'WAITING': 'info',
    'READY': 'success',
    'BATTLING': 'warning',
    'FINISHED': 'danger'
  }
  return typeMap[status] || 'info'
}

// ==================== 暴露给父组件的方法 ====================

defineExpose({
  openCreateDialog,
  openJoinDialog,
  openRoomListDialog
})
</script>

<template>
  <div class="room-manager">
    <!-- 创建房间弹窗 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建房间"
      width="500px"
      :before-close="() => createDialogVisible = false"
    >
      <el-form
        :model="createForm"
        label-width="100px"
        class="create-room-form"
      >
        <el-form-item label="难度范围">
          <div class="difficulty-range">
            <el-select
              v-model="createForm.minDifficulty"
              placeholder="最小难度"
              style="width: 150px"
              @change="onMinDifficultyChange"
            >
              <el-option
                v-for="option in difficultyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <span class="range-separator">至</span>
            <el-select
              v-model="createForm.maxDifficulty"
              placeholder="最大难度"
              style="width: 150px"
            >
              <el-option
                v-for="option in maxDifficultyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <div class="form-tip">
            选择题目的难度分数范围，系统将在此范围内筛选题目
            <br>
            当前选择：{{ createForm.minDifficulty }} - {{ createForm.maxDifficulty }}
          </div>
        </el-form-item>

        <el-form-item label="排除标签">
          <el-select
            v-model="createForm.excludedTags"
            :multiple="true"
            collapse-tags
            collapse-tags-tooltip
            placeholder="选择不想要的题目标签（可多选）"
            style="width: 100%"
            clearable
            filterable
            reserve-keyword
            max-collapse-tags="3"
            @change="onTagSelectionChange"
          >
            <el-option
              v-for="tag in allTags"
              :key="`tag-${tag.id}`"
              :label="tag.tagName"
              :value="tag.id"
            />
          </el-select>
          <div class="form-tip">
            选择不想出现的题目标签，系统将避免选择包含这些标签的题目
            <br>
            <strong>已选择 {{ createForm.excludedTags?.length || 0 }} 个标签</strong>
            <span v-if="createForm.excludedTags?.length > 0">
              : {{ allTags.filter(tag => createForm.excludedTags.includes(tag.id)).map(tag => tag.tagName).join(', ') }}
            </span>
            <br>
            <small style="color: #909399;">调试信息: {{ JSON.stringify(createForm.excludedTags) }}</small>
          </div>
        </el-form-item>

        <el-form-item label="题目选择">
          <el-radio-group v-model="problemSelectionMode" @change="onProblemSelectionModeChange">
            <el-radio value="random">根据条件随机选择</el-radio>
            <el-radio value="manual">手动指定题目ID</el-radio>
          </el-radio-group>

          <el-input
            v-if="problemSelectionMode === 'manual'"
            v-model="createForm.problemId"
            placeholder="请输入题目ID"
            type="number"
            style="margin-top: 10px"
          />

          <div class="form-tip">
            <span v-if="problemSelectionMode === 'random'">
              系统将根据上述难度范围和排除标签条件随机选择题目
            </span>
            <span v-else>
              请输入具体的题目ID，系统将使用指定的题目（忽略难度和标签条件）
            </span>
          </div>
        </el-form-item>

        <el-form-item label="房间描述">
          <el-input
            v-model="createForm.description"
            placeholder="欢迎来到我的房间"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleCreateRoom" 
            :loading="creating"
            :icon="Plus"
          >
            创建房间
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 加入房间弹窗 -->
    <el-dialog
      v-model="joinDialogVisible"
      title="加入房间"
      width="400px"
      :before-close="() => joinDialogVisible = false"
    >
      <el-form
        :model="joinForm"
        label-width="80px"
        class="join-room-form"
      >
        <el-form-item label="房间码">
          <el-input
            v-model="joinForm.roomCode"
            placeholder="请输入6位数字房间码"
            maxlength="6"
            clearable
          />
          <div class="form-tip">
            请输入房主提供的6位数字房间码
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="joinDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleJoinRoom" 
            :loading="joining"
          >
            加入房间
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 房间列表弹窗 -->
    <el-dialog
      v-model="roomListDialogVisible"
      title="活跃房间列表"
      width="700px"
      :before-close="() => roomListDialogVisible = false"
    >
      <div class="room-list" v-loading="loadingRooms">
        <div v-if="activeRooms.length === 0" class="empty-rooms">
          <el-empty description="暂无活跃房间" />
        </div>
        
        <div v-else class="rooms-grid">
          <div 
            v-for="room in activeRooms" 
            :key="room.roomCode"
            class="room-card"
          >
            <div class="room-header">
              <div class="room-code">房间码: {{ room.roomCode }}</div>
              <el-tag :type="getRoomStatusType(room.status)" size="small">
                {{ formatRoomStatus(room.status) }}
              </el-tag>
            </div>
            
            <div class="room-info">
              <div class="room-creator">
                <el-avatar :src="room.creator.avatar" :size="30" />
                <span>{{ room.creator.codeforcesId }}</span>
              </div>
              
              <div class="room-stats">
                <div class="stat-item">
                  <el-icon><Avatar /></el-icon>
                  <span>{{ room.participantCount }}/{{ room.maxParticipants }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ new Date(room.createTime).toLocaleTimeString() }}</span>
                </div>
              </div>
            </div>
            
            <div class="room-description" v-if="room.description">
              {{ room.description }}
            </div>
            
            <div class="room-actions">
              <el-button 
                type="primary" 
                size="small"
                @click="quickJoinRoom(room)"
                :disabled="room.participantCount >= room.maxParticipants"
              >
                {{ room.participantCount >= room.maxParticipants ? '房间已满' : '加入房间' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="loadActiveRooms" :loading="loadingRooms">刷新</el-button>
          <el-button @click="roomListDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.room-manager {
  // 表单样式
  .create-room-form,
  .join-room-form {
    padding: 20px 0;

    .form-tip {
      margin-top: 5px;
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }

    .difficulty-range {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-select {
        flex: 1;
      }

      .range-separator {
        color: #606266;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }

  // 弹窗底部样式
  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }

  // 房间列表样式
  .room-list {
    min-height: 300px;

    .empty-rooms {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .rooms-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;

      .room-card {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        background: #fff;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
        }

        .room-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .room-code {
            font-weight: 600;
            color: #303133;
            font-size: 16px;
          }
        }

        .room-info {
          margin-bottom: 12px;

          .room-creator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            span {
              font-weight: 500;
              color: #606266;
            }
          }

          .room-stats {
            display: flex;
            gap: 16px;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #909399;

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }

        .room-description {
          font-size: 14px;
          color: #606266;
          line-height: 1.4;
          margin-bottom: 12px;
          padding: 8px;
          background: #f5f7fa;
          border-radius: 4px;
          border-left: 3px solid #409eff;
        }

        .room-actions {
          text-align: right;

          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
