<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Download, 
  Upload,
  Warning
} from '@element-plus/icons-vue'
import { updateProblemsFromCodeforces, updateTagsFromCodeforces, getDataUpdateStatus } from '@/api/api'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref({
  problems: false,
  tags: false,
  status: false
})

// 更新状态
const updateStatus = ref({
  problems: {
    lastUpdate: null,
    totalCount: 0,
    status: 'idle' // idle, updating, success, error
  },
  tags: {
    lastUpdate: null,
    totalCount: 0,
    status: 'idle'
  }
})

// 更新配置
const updateConfig = ref({
  problems: {
    maxPages: 10,
    minRating: 800,
    maxRating: 3500
  },
  tags: {
    forceUpdate: false
  }
})

// ==================== 核心业务方法 ====================

/**
 * 获取数据更新状态
 */
const loadUpdateStatus = async () => {
  loading.value.status = true
  
  try {
    const response = await getDataUpdateStatus()
    
    if (response.status && response.data) {
      updateStatus.value = response.data
    } else {
      ElMessage.error('获取更新状态失败')
    }
    
  } catch (error) {
    console.error('❌ 获取更新状态失败:', error)
    ElMessage.error('获取更新状态失败: ' + error.message)
  } finally {
    loading.value.status = false
  }
}

/**
 * 更新题目数据
 */
const updateProblems = async () => {
  ElMessageBox.confirm(
    `确定要从Codeforces更新题目数据吗？\n这可能需要几分钟时间。\n\n配置：\n- 最大页数: ${updateConfig.value.problems.maxPages}\n- 难度范围: ${updateConfig.value.problems.minRating}-${updateConfig.value.problems.maxRating}`,
    '确认更新题目',
    {
      confirmButtonText: '开始更新',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value.problems = true
    updateStatus.value.problems.status = 'updating'
    
    try {
      const response = await updateProblemsFromCodeforces(updateConfig.value.problems)
      
      if (response.status) {
        ElMessage.success('题目数据更新成功！')
        updateStatus.value.problems.status = 'success'
        updateStatus.value.problems.lastUpdate = new Date().toLocaleString()
        updateStatus.value.problems.totalCount = response.data.totalCount || 0
      } else {
        ElMessage.error(response.message || '题目数据更新失败')
        updateStatus.value.problems.status = 'error'
      }
      
    } catch (error) {
      console.error('❌ 更新题目数据失败:', error)
      ElMessage.error('更新题目数据失败: ' + error.message)
      updateStatus.value.problems.status = 'error'
    } finally {
      loading.value.problems = false
    }
  }).catch(() => {
    // 用户取消
  })
}

/**
 * 更新标签数据
 */
const updateTags = async () => {
  ElMessageBox.confirm(
    `确定要从Codeforces更新标签数据吗？\n\n配置：\n- 强制更新: ${updateConfig.value.tags.forceUpdate ? '是' : '否'}`,
    '确认更新标签',
    {
      confirmButtonText: '开始更新',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value.tags = true
    updateStatus.value.tags.status = 'updating'
    
    try {
      const response = await updateTagsFromCodeforces(updateConfig.value.tags)
      
      if (response.status) {
        ElMessage.success('标签数据更新成功！')
        updateStatus.value.tags.status = 'success'
        updateStatus.value.tags.lastUpdate = new Date().toLocaleString()
        updateStatus.value.tags.totalCount = response.data.totalCount || 0
      } else {
        ElMessage.error(response.message || '标签数据更新失败')
        updateStatus.value.tags.status = 'error'
      }
      
    } catch (error) {
      console.error('❌ 更新标签数据失败:', error)
      ElMessage.error('更新标签数据失败: ' + error.message)
      updateStatus.value.tags.status = 'error'
    } finally {
      loading.value.tags = false
    }
  }).catch(() => {
    // 用户取消
  })
}

/**
 * 获取状态标签类型
 */
const getStatusType = (status) => {
  const typeMap = {
    'idle': 'info',
    'updating': 'warning',
    'success': 'success',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const textMap = {
    'idle': '待更新',
    'updating': '更新中...',
    'success': '更新成功',
    'error': '更新失败'
  }
  return textMap[status] || '未知状态'
}

// ==================== 生命周期钩子 ====================

onMounted(() => {
  loadUpdateStatus()
})
</script>

<template>
  <div class="data-management">
    <el-card class="header-card" shadow="never">
      <template #header>
        <div class="header">
          <h2>数据管理</h2>
          <el-button 
            @click="loadUpdateStatus" 
            :loading="loading.status"
            :icon="Refresh"
          >
            刷新状态
          </el-button>
        </div>
      </template>
      
      <el-alert
        title="数据更新说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>通过Python服务从Codeforces获取最新的题目和标签数据，更新到本地数据库。</p>
          <p><strong>注意：</strong>更新过程可能需要几分钟时间，请耐心等待。</p>
        </template>
      </el-alert>
    </el-card>

    <!-- 题目数据管理 -->
    <el-card class="data-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>题目数据管理</h3>
          <el-tag :type="getStatusType(updateStatus.problems.status)" size="large">
            {{ getStatusText(updateStatus.problems.status) }}
          </el-tag>
        </div>
      </template>
      
      <div class="data-info">
        <div class="info-item">
          <span class="label">最后更新时间：</span>
          <span>{{ updateStatus.problems.lastUpdate || '从未更新' }}</span>
        </div>
        <div class="info-item">
          <span class="label">题目总数：</span>
          <span>{{ updateStatus.problems.totalCount }}</span>
        </div>
      </div>
      
      <el-divider />
      
      <div class="config-section">
        <h4>更新配置</h4>
        <el-form :model="updateConfig.problems" label-width="120px">
          <el-form-item label="最大页数">
            <el-input-number 
              v-model="updateConfig.problems.maxPages" 
              :min="1" 
              :max="50"
              :disabled="loading.problems"
            />
            <div class="form-tip">限制获取的页数，避免请求过多数据</div>
          </el-form-item>
          
          <el-form-item label="最小难度">
            <el-input-number 
              v-model="updateConfig.problems.minRating" 
              :min="800" 
              :max="3500"
              :step="100"
              :disabled="loading.problems"
            />
          </el-form-item>
          
          <el-form-item label="最大难度">
            <el-input-number 
              v-model="updateConfig.problems.maxRating" 
              :min="800" 
              :max="3500"
              :step="100"
              :disabled="loading.problems"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          @click="updateProblems"
          :loading="loading.problems"
          :icon="Download"
          size="large"
        >
          {{ loading.problems ? '更新中...' : '更新题目数据' }}
        </el-button>
      </div>
    </el-card>

    <!-- 标签数据管理 -->
    <el-card class="data-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>标签数据管理</h3>
          <el-tag :type="getStatusType(updateStatus.tags.status)" size="large">
            {{ getStatusText(updateStatus.tags.status) }}
          </el-tag>
        </div>
      </template>
      
      <div class="data-info">
        <div class="info-item">
          <span class="label">最后更新时间：</span>
          <span>{{ updateStatus.tags.lastUpdate || '从未更新' }}</span>
        </div>
        <div class="info-item">
          <span class="label">标签总数：</span>
          <span>{{ updateStatus.tags.totalCount }}</span>
        </div>
      </div>
      
      <el-divider />
      
      <div class="config-section">
        <h4>更新配置</h4>
        <el-form :model="updateConfig.tags" label-width="120px">
          <el-form-item label="强制更新">
            <el-switch 
              v-model="updateConfig.tags.forceUpdate"
              :disabled="loading.tags"
            />
            <div class="form-tip">是否覆盖已存在的标签数据</div>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          @click="updateTags"
          :loading="loading.tags"
          :icon="Upload"
          size="large"
        >
          {{ loading.tags ? '更新中...' : '更新标签数据' }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.data-management {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .data-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #303133;
      }
    }

    .data-info {
      margin-bottom: 20px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 120px;
        }
      }
    }

    .config-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 16px 0;
        color: #303133;
      }

      .form-tip {
        margin-top: 5px;
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
    }

    .action-buttons {
      text-align: center;

      .el-button {
        min-width: 160px;
      }
    }
  }
}
</style>
