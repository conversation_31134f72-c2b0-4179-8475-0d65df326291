<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2d0a03d3-aa6d-4ecf-880d-a15308891a0d" name="变更" comment="查询消息及关联用户信息" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/master" />
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="hzf" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2zzRlMXHrv057tXqIqtNqZFbC62" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/CodeDuel/front/code-fuel-backend/pom.xml&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15429688&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;a5ea9ac96865dd47e04ad5952ba8ea16&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\CodeDuel\remote\code-fuel-backend" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.codeduelApplication">
    <configuration name="CodeGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xju.codeduel.CodeGenerator" />
      <module name="codeduel" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xju.codeduel.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="generated-requests | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" requestIdentifier="#1" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="codeduelApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="codeduel" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xju.codeduel.codeduelApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CodeGenerator" />
        <item itemvalue="HTTP 请求.generated-requests | #1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2d0a03d3-aa6d-4ecf-880d-a15308891a0d" name="变更" comment="" />
      <created>1752732066012</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752732066012</updated>
      <workItem from="1752732067410" duration="1114000" />
      <workItem from="1752733207416" duration="8366000" />
      <workItem from="1752803857321" duration="4344000" />
      <workItem from="1752821108765" duration="493000" />
      <workItem from="1752821629983" duration="1012000" />
      <workItem from="1752822848536" duration="9340000" />
      <workItem from="1752888849195" duration="20163000" />
      <workItem from="1752923452355" duration="1495000" />
      <workItem from="1752975971042" duration="18012000" />
      <workItem from="1753016314077" duration="2883000" />
      <workItem from="1753025807933" duration="1567000" />
      <workItem from="1753086362560" duration="1248000" />
      <workItem from="1753504747565" duration="1362000" />
      <workItem from="1753578863984" duration="32000" />
    </task>
    <task id="LOCAL-00001" summary="///">
      <created>1752732632618</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752732632618</updated>
    </task>
    <task id="LOCAL-00002" summary="注册">
      <created>1752733751672</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752733751672</updated>
    </task>
    <task id="LOCAL-00003" summary="注册">
      <created>1752734356287</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752734356287</updated>
    </task>
    <task id="LOCAL-00004" summary="用户列表">
      <created>1752737046225</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752737046225</updated>
    </task>
    <task id="LOCAL-00005" summary="建立帖子-用户对象">
      <created>1752827322790</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752827322790</updated>
    </task>
    <task id="LOCAL-00006" summary="建立帖子-用户接口">
      <created>1752830775579</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752830775579</updated>
    </task>
    <task id="LOCAL-00007" summary="分页查询帖子">
      <created>1752895366488</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752895366488</updated>
    </task>
    <task id="LOCAL-00008" summary="新增帖子">
      <created>1752901345547</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752901345547</updated>
    </task>
    <task id="LOCAL-00009" summary="查询用户及其Rating变化历史">
      <created>1752920529538</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752920529538</updated>
    </task>
    <task id="LOCAL-00010" summary="查询消息及关联用户信息">
      <created>1752990979952</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752990979952</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="hzf" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="///" />
    <MESSAGE value="注册" />
    <MESSAGE value="用户列表" />
    <MESSAGE value="建立帖子-用户对象" />
    <MESSAGE value="建立帖子-用户接口" />
    <MESSAGE value="分页查询帖子" />
    <MESSAGE value="新增帖子" />
    <MESSAGE value="查询用户及其Rating变化历史" />
    <MESSAGE value="查询消息及关联用户信息" />
    <option name="LAST_COMMIT_MESSAGE" value="查询消息及关联用户信息" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>