package com.xju.codeduel.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 数据库初始化器
 * 在应用启动时执行数据库初始化脚本
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        // 检查是否需要初始化数据库
        try {
            // 尝试查询users表，如果失败说明需要初始化
            jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
            System.out.println("✅ 数据库已存在，跳过初始化");
        } catch (Exception e) {
            System.out.println("🔄 数据库不存在或结构不完整，开始初始化...");
            initializeDatabase();
        }
    }

    private void initializeDatabase() {
        try {
            // 读取SQL文件
            ClassPathResource resource = new ClassPathResource("db.sql");
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)
            );

            StringBuilder sqlBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sqlBuilder.append(line).append("\n");
            }
            reader.close();

            // 分割SQL语句并执行
            String[] sqlStatements = sqlBuilder.toString().split(";");
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty() && !sql.startsWith("--") && !sql.startsWith("/*")) {
                    try {
                        jdbcTemplate.execute(sql);
                    } catch (Exception e) {
                        System.err.println("❌ 执行SQL失败: " + sql);
                        System.err.println("错误: " + e.getMessage());
                    }
                }
            }

            System.out.println("✅ 数据库初始化完成");
        } catch (Exception e) {
            System.err.println("❌ 数据库初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
