package com.xju.codeduel.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.BattleRecordWithDetailsDTO;
import com.xju.codeduel.model.dto.PageDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IBattleRecordsService;
import com.xju.codeduel.model.domain.BattleRecords;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/battleRecords")
public class BattleRecordsController {

    private final Logger logger = LoggerFactory.getLogger( BattleRecordsController.class );

    @Autowired
    private IBattleRecordsService battleRecordsService;


    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<BattleRecords> getById(@PathVariable("id") Long id)throws Exception {
        BattleRecords battleRecords = battleRecordsService.getById(id);
        return JsonResponse.success(battleRecords);
    }

    /**
     * 获取最近的对战记录
     */
    @GetMapping("/recent")
    @ResponseBody
    public JsonResponse<Page<BattleRecordWithDetailsDTO>> getRecentBattleRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            PageDTO pageDTO = new PageDTO();
            pageDTO.setPageNo(page);
            pageDTO.setPageSize(size);

            Page<BattleRecordWithDetailsDTO> result = battleRecordsService.getRecentBattleRecords(pageDTO);
            return JsonResponse.success(result);
        } catch (Exception e) {
            return JsonResponse.failure("获取对战记录失败: " + e.getMessage());
        }
    }
}

