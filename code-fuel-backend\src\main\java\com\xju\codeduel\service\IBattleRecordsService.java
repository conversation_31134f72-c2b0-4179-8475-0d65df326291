package com.xju.codeduel.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.BattleRecords;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xju.codeduel.model.dto.BattleRecordWithDetailsDTO;
import com.xju.codeduel.model.dto.PageDTO;

/**
 * <p>
 * 对战记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IBattleRecordsService extends IService<BattleRecords> {

    /**
     * 获取最近的对战记录
     * @param pageDTO 分页参数
     * @return 对战记录详情列表
     */
    Page<BattleRecordWithDetailsDTO> getRecentBattleRecords(PageDTO pageDTO);

}
