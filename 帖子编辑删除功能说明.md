# 帖子编辑、删除和置顶功能实现说明

## 🎯 功能概述

成功为 Forum 讨论区添加了帖子编辑、删除和置顶功能，支持权限控制：

- **管理员**：可以编辑、删除和置顶任意帖子
- **普通用户**：只能编辑和删除自己发布的帖子，无法置顶帖子

## ✅ 功能特性

### 1. **权限控制**

- 基于用户的`isAdmin`字段判断管理员权限
- 普通用户只能看到和操作自己发布的帖子
- 管理员可以看到所有帖子的编辑/删除/置顶按钮
- 置顶功能仅限管理员使用

### 2. **安全机制**

- 前端权限判断：`canManagePost(post)` 方法
- 后端双重权限验证：检查用户 ID 和管理员状态
- 删除前弹出确认对话框
- 逻辑删除（设置`is_deleted=1`）而非物理删除

### 3. **用户体验**

- 编辑功能复用创建帖子的 Markdown 编辑器
- 支持实时预览和分屏编辑
- 编辑时预填充原有内容
- 置顶帖子在列表顶部显示，有特殊的视觉样式
- 操作成功后自动刷新帖子列表

## 🔧 技术实现

### 后端实现

#### 新增 API 接口

**1. 更新帖子接口**

```
PUT /api/posts/{postId}?userId={userId}&isAdmin={isAdmin}

参数：
- postId: 帖子ID（路径参数）
- userId: 当前用户ID（查询参数）
- isAdmin: 是否为管理员（查询参数，默认false）

请求体：
{
  "title": "更新后的标题",
  "content": "更新后的内容"
}

响应：
{
  "status": true/false,
  "message": "更新成功/失败原因",
  "data": Posts对象
}
```

**2. 删除帖子接口**

```
DELETE /api/posts/{postId}?userId={userId}&isAdmin={isAdmin}

参数：
- postId: 帖子ID（路径参数）
- userId: 当前用户ID（查询参数）
- isAdmin: 是否为管理员（查询参数，默认false）

响应：
{
  "status": true/false,
  "message": "删除成功/失败原因",
  "data": true/false
}
```

**3. 切换置顶状态接口**

```
PUT /api/posts/{postId}/pin?userId={userId}&isAdmin={isAdmin}

参数：
- postId: 帖子ID（路径参数）
- userId: 当前用户ID（查询参数）
- isAdmin: 是否为管理员（查询参数，默认false）

响应：
{
  "status": true/false,
  "message": "操作成功/失败原因",
  "data": Posts对象
}
```

#### 权限验证逻辑

```java
// 权限检查：管理员可以操作任意帖子，普通用户只能操作自己的帖子
if (!isAdmin && !existingPost.getUserId().equals(userId)) {
    return JsonResponse.failure("无权限操作此帖子");
}
```

### 前端实现

#### 新增 API 方法

```javascript
// 更新帖子
export const updatePost = (postId, post, params) =>
  request.put(`/api/posts/${postId}`, post, { params });

// 删除帖子
export const deletePost = (postId, params) =>
  request.delete(`/api/posts/${postId}`, { params });

// 切换置顶状态
export const togglePinPost = (postId, params) =>
  request.put(`/api/posts/${postId}/pin`, {}, { params });
```

#### 权限判断逻辑

```javascript
const canManagePost = (post) => {
  // 如果没有当前用户信息，不显示管理操作
  if (!currentUser.value) {
    return false;
  }

  // 管理员可以管理所有帖子
  if (currentUser.value.isAdmin) {
    return true;
  }

  // 普通用户只能管理自己的帖子
  return post.author.id === currentUser.value.id;
};

// 检查是否可以置顶帖子（只有管理员可以）
const canPinPost = () => {
  return currentUser.value && currentUser.value.isAdmin;
};
```

#### 编辑功能实现

- 复用创建帖子的对话框组件
- 独立的编辑表单数据：`editForm`
- 独立的 Markdown 编辑器引用：`editContentEditor`
- 支持预览和分屏编辑模式

#### 删除功能实现

- 确认对话框防止误删
- 调用后端 API 进行逻辑删除
- 操作成功后刷新帖子列表

#### 置顶功能实现

- 只有管理员可以看到置顶/取消置顶选项
- 支持切换置顶状态（置顶 ↔ 取消置顶）
- 置顶帖子在列表顶部单独显示
- 置顶帖子有特殊的视觉样式（金色背景）

## 📝 使用说明

### 编辑帖子

1. 在帖子列表中，点击帖子右侧的"更多"按钮（三个点）
2. 选择"编辑"选项
3. 在弹出的编辑对话框中修改标题和内容
4. 支持 Markdown 格式，可以使用工具栏快速插入格式
5. 可以切换到预览模式查看效果
6. 点击"更新帖子"按钮保存修改

### 删除帖子

1. 在帖子列表中，点击帖子右侧的"更多"按钮（三个点）
2. 选择"删除"选项（红色文字）
3. 在确认对话框中点击"确定"
4. 帖子将被逻辑删除，不再显示在列表中

### 置顶帖子

1. 在帖子列表中，点击帖子右侧的"更多"按钮（三个点）
2. 选择"置顶"选项（仅管理员可见）
3. 帖子将被置顶，移动到列表顶部的置顶区域
4. 要取消置顶，点击"取消置顶"选项

### 权限说明

- **普通用户**：只能看到自己发布的帖子的编辑/删除按钮，无法看到置顶选项
- **管理员**：可以看到所有帖子的编辑/删除/置顶按钮
- 如果用户没有权限，操作按钮不会显示

## 🔒 安全特性

1. **前端权限控制**：根据用户身份显示/隐藏操作按钮
2. **后端权限验证**：双重检查用户权限，防止绕过前端限制
3. **参数验证**：检查必要参数的完整性和有效性
4. **逻辑删除**：保留数据完整性，支持数据恢复
5. **错误处理**：完善的错误提示和异常处理

## 🚀 后续优化建议

1. **批量操作**：支持批量删除帖子
2. **版本控制**：记录帖子编辑历史
3. **审核机制**：编辑后的帖子可能需要重新审核
4. **通知功能**：帖子被编辑或删除时通知相关用户
5. **回收站**：为删除的帖子提供回收站功能
