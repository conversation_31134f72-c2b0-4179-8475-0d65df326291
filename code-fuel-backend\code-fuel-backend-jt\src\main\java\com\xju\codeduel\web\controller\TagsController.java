package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.ITagsService;
import com.xju.codeduel.model.domain.Tags;
import java.util.List;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/tags")
public class TagsController {

    private final Logger logger = LoggerFactory.getLogger( TagsController.class );

    @Autowired
    private ITagsService tagsService;


    /**
     * 获取所有标签
     *
     * 功能说明：
     * 1. 查询数据库中所有可用的标签
     * 2. 用于前端标签选择组件
     * 3. 支持房间创建时的标签排除功能
     *
     * @return 所有标签列表
     */
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<List<Tags>> getAllTags() {
        logger.info("📋 查询所有标签");

        try {
            // 查询所有标签
            List<Tags> tags = tagsService.list();

            logger.info("✅ 查询到 {} 个标签", tags.size());
            return JsonResponse.success(tags);

        } catch (Exception e) {
            logger.error("❌ 查询标签失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询标签失败: " + e.getMessage());
        }
    }

    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Tags> getById(@PathVariable("id") Long id)throws Exception {
        Tags tags = tagsService.getById(id);
        return JsonResponse.success(tags);
    }
}

