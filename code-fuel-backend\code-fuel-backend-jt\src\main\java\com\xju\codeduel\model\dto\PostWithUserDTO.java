package com.xju.codeduel.model.dto;

import com.xju.codeduel.model.domain.Posts;
import com.xju.codeduel.model.domain.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



@Data
@ApiModel(value = "PostWithUserDTO对象", description = "包含帖子和用户信息的DTO")
public class PostWithUserDTO {
    @ApiModelProperty(value = "帖子信息")
    private Posts post;

    @ApiModelProperty(value = "发帖用户信息")
    private Users user;
}
