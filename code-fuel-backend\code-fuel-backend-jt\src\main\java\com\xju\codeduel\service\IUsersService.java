package com.xju.codeduel.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Users;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.UserDTO;
import com.xju.codeduel.model.dto.UserRatingChangeDTO;

import java.util.Map;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IUsersService extends IService<Users> {

    Users login(Users users);

    Page<Users> pageList(PageDTO pageDTO, UserDTO users);

    Users getUserByUsername(String username);


    Page<UserRatingChangeDTO> getUsersWithRatingHistory(PageDTO pageDTO, String username);

    Page<UserDTO> getUsersListWithBattleCount(PageDTO pageDTO, String username);

    UserRatingChangeDTO getUserRatingHistoryByUsername(String username);

    /**
     * 获取首页统计数据
     * @return 包含各种统计信息的Map
     */
    Map<String, Object> getHomeStats();

}
