package com.xju.codeduel.service;

import com.xju.codeduel.model.domain.Problems;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 题目信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IProblemsService extends IService<Problems> {

    /**
     * 根据难度和标签筛选题目
     *
     * 功能说明：
     * 1. 根据难度范围筛选题目
     * 2. 排除指定的标签
     * 3. 返回符合条件的题目列表
     *
     * @param minDifficulty 最小难度分数
     * @param maxDifficulty 最大难度分数
     * @param excludedTagIds 排除的标签ID列表
     * @return 筛选后的题目列表
     */
    List<Problems> filterProblems(Integer minDifficulty, Integer maxDifficulty, List<Long> excludedTagIds);

}
