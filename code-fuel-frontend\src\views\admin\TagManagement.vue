<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Upload,
  PriceTag,
  Warning
} from '@element-plus/icons-vue'
import { getTagsPage, updateTagsFromCodeforces } from '@/api/api'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref({
  table: false,
  update: false
})

// 标签列表数据
const tagData = reactive({
  list: [],
  total: 0,
  current: 1,
  size: 10
})

// 搜索表单
const searchForm = reactive({
  tagName: ''
})

// 更新配置
const updateConfig = reactive({
  forceUpdate: false
})

// ==================== 方法定义 ====================

/**
 * 加载标签列表数据
 * 
 * 功能说明：
 * 1. 根据当前分页和搜索条件查询标签
 * 2. 支持标签名模糊搜索
 * 3. 更新表格数据和分页信息
 */
const loadTags = async () => {
  loading.value.table = true
  
  try {
    console.log('🔍 加载标签列表，参数:', {
      pageNo: tagData.current,
      pageSize: tagData.size,
      ...searchForm
    })
    
    const params = {
      pageNo: tagData.current,
      pageSize: tagData.size,
      tagName: searchForm.tagName || undefined
    }
    
    const response = await getTagsPage(params)
    
    if (response.status) {
      const data = response.data
      tagData.list = data.records || []
      tagData.total = data.total || 0
      tagData.current = data.current || 1
      
      console.log('✅ 标签列表加载成功，共', tagData.total, '条记录')
    } else {
      ElMessage.error(response.message || '加载标签列表失败')
    }
    
  } catch (error) {
    console.error('❌ 加载标签列表失败:', error)
    ElMessage.error('加载标签列表失败: ' + error.message)
  } finally {
    loading.value.table = false
  }
}

/**
 * 搜索标签
 * 
 * 功能说明：
 * 1. 重置到第一页
 * 2. 根据搜索条件重新加载数据
 */
const handleSearch = () => {
  console.log('🔍 执行标签搜索，条件:', searchForm)
  tagData.current = 1
  loadTags()
}

/**
 * 重置搜索条件
 * 
 * 功能说明：
 * 1. 清空所有搜索条件
 * 2. 重新加载数据
 */
const handleReset = () => {
  console.log('🔄 重置搜索条件')
  searchForm.tagName = ''
  tagData.current = 1
  loadTags()
}

/**
 * 分页变化处理
 * 
 * 功能说明：
 * 1. 更新当前页码
 * 2. 重新加载数据
 */
const handlePageChange = (page) => {
  console.log('📄 切换到第', page, '页')
  tagData.current = page
  loadTags()
}

/**
 * 每页大小变化处理
 * 
 * 功能说明：
 * 1. 更新每页大小
 * 2. 重置到第一页
 * 3. 重新加载数据
 */
const handleSizeChange = (size) => {
  console.log('📏 每页大小变更为', size)
  tagData.size = size
  tagData.current = 1
  loadTags()
}

/**
 * 更新标签数据
 * 
 * 功能说明：
 * 1. 调用Python服务从Codeforces更新标签数据
 * 2. 更新完成后刷新列表
 * 3. 支持强制更新配置
 */
const handleUpdateTags = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要从Codeforces更新标签数据吗？这个过程可能需要几分钟时间。',
      '确认更新',
      {
        confirmButtonText: '确定更新',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '更新中...'
          }
          done()
        }
      }
    )
    
    loading.value.update = true
    
    console.log('🔄 开始更新标签数据，配置:', updateConfig)
    
    const response = await updateTagsFromCodeforces(updateConfig)
    
    if (response.status) {
      ElMessage.success('标签数据更新成功！')
      console.log('✅ 标签数据更新完成:', response.data)
      
      // 刷新列表
      await loadTags()
    } else {
      ElMessage.error(response.message || '更新标签数据失败')
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 更新标签数据失败:', error)
      ElMessage.error('更新标签数据失败: ' + error.message)
    }
  } finally {
    loading.value.update = false
  }
}

/**
 * 格式化时间显示
 * 
 * 功能说明：
 * 1. 将时间戳转换为可读格式
 * 2. 处理空值情况
 */
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// ==================== 生命周期 ====================

onMounted(() => {
  console.log('🚀 标签管理页面初始化')
  loadTags()
})
</script>

<template>
  <div class="tag-management">
    <!-- 页面标题 -->
    <el-card class="header-card" shadow="never">
      <template #header>
        <div class="header">
          <h2>标签管理</h2>
          <el-button 
            type="primary"
            @click="handleUpdateTags"
            :loading="loading.update"
            :icon="Upload"
            size="large"
          >
            {{ loading.update ? '更新中...' : '更新标签数据' }}
          </el-button>
        </div>
      </template>
      
      <el-alert
        title="标签管理说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>管理系统中的标签数据，支持查看、搜索和更新功能。</p>
          <p><strong>更新功能：</strong>点击"更新标签数据"按钮可以从Codeforces获取最新标签数据。</p>
        </template>
      </el-alert>
    </el-card>

    <!-- 更新配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <span>更新配置</span>
      </template>
      
      <el-form :model="updateConfig" label-width="120px">
        <el-form-item label="强制更新">
          <el-switch 
            v-model="updateConfig.forceUpdate"
            :disabled="loading.update"
          />
          <div class="form-tip">是否覆盖已存在的标签数据</div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="标签名">
          <el-input
            v-model="searchForm.tagName"
            placeholder="请输入标签名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 标签列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>标签列表</span>
          <el-text type="info">共 {{ tagData.total }} 条记录</el-text>
        </div>
      </template>
      
      <el-table
        :data="tagData.list"
        :loading="loading.table"
        stripe
        style="width: 100%"
        empty-text="暂无标签数据"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="tagName" label="标签名" min-width="200">
          <template #default="{ row }">
            <el-tag size="large">
              <el-icon style="margin-right: 4px;"><PriceTag /></el-icon>
              {{ row.tagName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdTime" label="创建时间" width="200">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="tagData.current"
          v-model:page-size="tagData.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="tagData.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.tag-management {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .config-card {
    margin-bottom: 20px;

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
