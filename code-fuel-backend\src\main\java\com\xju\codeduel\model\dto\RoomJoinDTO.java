package com.xju.codeduel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 加入房间请求DTO
 * 用于接收前端加入房间的请求数据
 * 
 * 功能说明：
 * 1. 封装加入房间所需的基本信息
 * 2. 包含房间码和加入者信息
 * 3. 提供数据验证和类型安全
 * 
 * 使用场景：
 * - 用户输入房间码点击"加入房间"时发送此DTO到后端
 * - 后端根据房间码查找对应房间并验证是否可加入
 * - 将用户添加到房间的参与者列表中
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@ApiModel(value = "RoomJoinDTO", description = "加入房间请求数据")
public class RoomJoinDTO {

    @ApiModelProperty(value = "房间码", required = true, example = "123456")
    private Long roomCode;

    @ApiModelProperty(value = "加入者用户ID", required = true, example = "2")
    private Long userId;

    @ApiModelProperty(value = "加入者用户名", required = true, example = "jiangly")
    private String userName;

    /**
     * 默认构造函数
     */
    public RoomJoinDTO() {}

    /**
     * 全参构造函数
     * 
     * @param roomCode 房间码
     * @param userId 加入者用户ID
     * @param userName 加入者用户名
     */
    public RoomJoinDTO(Long roomCode, Long userId, String userName) {
        this.roomCode = roomCode;
        this.userId = userId;
        this.userName = userName;
    }

    // ==================== Getter和Setter方法 ====================

    public Long getRoomCode() {
        return roomCode;
    }

    public void setRoomCode(Long roomCode) {
        this.roomCode = roomCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 重写toString方法，便于日志记录和调试
     * 注意：不包含敏感信息
     */
    @Override
    public String toString() {
        return "RoomJoinDTO{" +
                "roomCode=" + roomCode +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                '}';
    }
}
