package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.BattleRecords;
import com.xju.codeduel.mapper.BattleRecordsMapper;
import com.xju.codeduel.model.dto.BattleRecordWithDetailsDTO;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.service.IBattleRecordsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 对战记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class BattleRecordsServiceImpl extends ServiceImpl<BattleRecordsMapper, BattleRecords> implements IBattleRecordsService {

    @Autowired
    private BattleRecordsMapper battleRecordsMapper;

    @Override
    public Page<BattleRecordWithDetailsDTO> getRecentBattleRecords(PageDTO pageDTO) {
        Page<BattleRecordWithDetailsDTO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        return battleRecordsMapper.getRecentBattleRecords(page);
    }

}
