<template>
  <div class="battle-container">
    <!-- 对战模式选择界面 -->
    <div class="mode-selection" v-if="!inBattle && !showRoomWaiting && !showBattleRoom">
      <div class="mode-header">
        <h1>选择对战模式</h1>
        <p>选择您喜欢的对战方式，开始激烈的代码竞技！</p>
      </div>

      <el-row :gutter="30" class="mode-cards">
        <el-col :span="12">
          <div class="mode-card random-match" @click="startRandomMatch">
            <div class="mode-card-bg"></div>
            <div class="mode-content">
              <div class="mode-icon-wrapper">
                <el-icon class="mode-icon"><Timer /></el-icon>
              </div>
              <h3>随机匹配</h3>
              <p>系统智能匹配实力相当的对手</p>
              <ul class="mode-features">
                <li>• 基于Rating智能匹配</li>
                <li>• 快速找到合适对手</li>
                <li>• 公平竞技环境</li>
              </ul>
              <div class="mode-button">
                <el-button type="primary" size="large" round>
                  <el-icon><Timer /></el-icon>
                  开始匹配
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="mode-card room-battle">
            <div class="mode-card-bg"></div>
            <div class="mode-content">
              <div class="mode-icon-wrapper">
                <el-icon class="mode-icon"><House /></el-icon>
              </div>
              <h3>房间对战</h3>
              <p>创建房间或加入好友的房间进行对战</p>
              <ul class="mode-features">
                <li>• 与好友私人对战</li>
                <li>• 自定义房间设置</li>
                <li>• 观战模式支持</li>
              </ul>
              <div class="room-buttons">
                <el-button type="success" size="large" round @click="openCreateRoom">
                  <el-icon><Plus /></el-icon>
                  创建房间
                </el-button>
                <el-button type="primary" size="large" round @click="openJoinRoom">
                  <el-icon><Right /></el-icon>
                  加入房间
                </el-button>
                <el-button type="info" size="large" round @click="openRoomList">
                  <el-icon><List /></el-icon>
                  房间列表
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 房间管理组件 -->
    <RoomManager
      ref="roomManagerRef"
      @room-created="handleRoomCreated"
      @room-joined="handleRoomJoined"
      @room-error="handleRoomError"
    />

    <!-- 房间等待界面 -->
    <RoomWaiting
      ref="roomWaitingRef"
      :visible="showRoomWaiting"
      :room-info="currentRoom"
      @close="handleRoomWaitingClose"
      @battle-start="handleBattleStart"
      @room-update="handleRoomUpdate"
    />

    <!-- 对战界面 -->
    <BattleRoom
      :visible="showBattleRoom"
      :room-info="currentRoom"
      @close="handleBattleRoomClose"
      @battle-end="handleBattleEnd"
    />

    <!-- 匹配中弹窗 -->
    <el-dialog
      v-model="isMatching"
      title="正在匹配对手"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleMatchDialogClose"
      center
      class="matching-dialog"
    >
      <div class="matching-content">
        <el-icon class="matching-icon rotating"><Loading /></el-icon>
        <h3>正在寻找合适的对手...</h3>
        <p class="matching-time">已等待时间：{{ matchingTime }}秒</p>
        <div class="matching-tips">
          <p>• 系统会根据您的Rating匹配合适的对手</p>
          <p>• 等待时间越长，匹配范围会适当放宽</p>
          <p>• 请耐心等待，通常在1分钟内完成匹配</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelMatchRequest" type="danger">
            取消匹配
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 匹配成功弹窗 -->
    <el-dialog
      v-model="showMatchSuccess"
      title="匹配成功！"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      center
      class="match-success-dialog"
    >
      <div class="match-success-content">
        <el-icon class="success-icon bounce"><SuccessFilled /></el-icon>
        <h2>找到对手了！</h2>
        <div class="opponent-info">
          <div class="opponent-avatar">
            <el-icon><User /></el-icon>
          </div>
          <h3>{{ matchSuccessInfo?.opponent || matchedOpponent }}</h3>
          <p>准备开始激烈的代码对决吧！</p>
        </div>
        <div class="countdown-display">
          <p>{{ countdown }}秒后自动进入对战房间</p>
          <div class="countdown-circle">
            <span class="countdown-number">{{ countdown }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" size="large" @click="enterBattleRoom">
            立即进入房间
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 对战结果 -->
    <el-dialog v-model="showResultDialog" title="对战结果" width="400px" :close-on-click-modal="false">
      <div class="battle-result">
        <div class="result-icon">
          <el-icon v-if="battleResult.isWin" class="win-icon"><Trophy /></el-icon>
          <el-icon v-else class="lose-icon"><Close /></el-icon>
        </div>
        <h3>{{ battleResult.isWin ? '恭喜获胜！' : '很遗憾失败了' }}</h3>
        <div class="rating-change">
          <span>Rating变化：</span>
          <span :class="battleResult.ratingChange > 0 ? 'rating-up' : 'rating-down'">
            {{ battleResult.ratingChange > 0 ? '+' : '' }}{{ battleResult.ratingChange }}
          </span>
        </div>
        <div class="battle-stats">
          <p>用时：{{ formatTime(battleResult.duration) }}</p>
          <p>提交次数：{{ battleResult.submissions }}</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="backToSelection">返回选择</el-button>
        <el-button type="primary" @click="startNewBattle">再来一局</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Timer,
  House,
  Loading,
  Trophy,
  Close,
  SuccessFilled,
  User,
  Plus,
  Right,
  List
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentRoomStatus, getRoomInfo, startMatch, cancelMatch, getMatchStatus, clearUserRoomStatus } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'
import RoomManager from '@/components/RoomManager.vue'
import RoomWaiting from '@/components/RoomWaiting.vue'
import BattleRoom from '@/components/BattleRoom.vue'

// 路由和状态管理
const router = useRouter()
const userInfoStore = useUserInfoStore()

// 状态管理
const inBattle = ref(false)
const isMatching = ref(false)
const matchingTime = ref(0)
const battleTime = ref(0)
const showResultDialog = ref(false)
const showMatchSuccess = ref(false)

// 房间管理组件引用
const roomManagerRef = ref()
const roomWaitingRef = ref()

// 当前房间信息
const currentRoom = ref(null)

// 房间等待界面显示状态
const showRoomWaiting = ref(false)

// 对战界面显示状态
const showBattleRoom = ref(false)

// 移除旧的表单数据，使用新的房间管理组件

// 对战数据
const currentUser = ref({
  codeforcesId: 'user123',
  avatar: '',
  rating: 1500
})

const opponent = ref({
  codeforcesId: 'opponent456',
  avatar: '',
  rating: 1520
})


const selectedLanguage = ref('cpp')
const userCode = ref('')
const runResult = ref(null)
const battleResult = ref({
  isWin: true,
  ratingChange: 25,
  duration: 300,
  submissions: 2
})

// 定时器
let matchingTimer = null
let battleTimer = null
let matchStatusTimer = null

// 匹配状态
const matchingStatus = ref('NOT_MATCHING') // NOT_MATCHING, WAITING, MATCHED, SUCCESS
const matchingMessage = ref('')
const matchSuccessInfo = ref(null) // 匹配成功信息
const countdown = ref(0) // 倒计时

// 方法
const startRandomMatch = async () => {
  const currentUser = userInfoStore.userInfo
  if (!currentUser) {
    ElMessage.error('请先登录')
    return
  }

  try {
    isMatching.value = true
    matchingTime.value = 0
    matchingStatus.value = 'WAITING'
    matchingMessage.value = '正在寻找对手...'

    // 先清理用户房间状态，防止状态残留
    try {
      await clearUserRoomStatus({ userId: currentUser.id })
      console.log('✅ 用户房间状态已清理')
    } catch (error) {
      console.warn('⚠️ 清理用户房间状态失败，继续匹配:', error.message)
    }

    // 调用后端开始匹配
    const response = await startMatch({
      userId: currentUser.id,
      minDifficulty: 800,
      maxDifficulty: 2000
    })

    if (response.status && response.data) {
      const result = response.data

      if (result.status === 'MATCHED') {
        // 立即匹配成功
        handleMatchSuccess(result)
      } else if (result.status === 'WAITING') {
        // 进入等待状态，开始轮询匹配状态
        startMatchStatusPolling()
        startMatchingTimer()
        ElMessage.success('已加入匹配队列，正在寻找对手...')
      }
    } else {
      throw new Error(response.message || '开始匹配失败')
    }

  } catch (error) {
    console.error('❌ 开始匹配失败:', error)
    ElMessage.error('开始匹配失败: ' + error.message)
    isMatching.value = false
    matchingStatus.value = 'NOT_MATCHING'
  }
}

const cancelMatchRequest = async () => {
  const currentUser = userInfoStore.userInfo
  if (!currentUser) {
    return
  }

  try {
    // 调用后端取消匹配
    await cancelMatch({
      userId: currentUser.id
    })

    // 清理前端状态
    stopMatchingTimer()
    stopMatchStatusPolling()
    isMatching.value = false
    matchingTime.value = 0
    matchingStatus.value = 'NOT_MATCHING'
    matchingMessage.value = ''

    // ElMessage.success('已取消匹配')

  } catch (error) {
    console.error('❌ 取消匹配失败:', error)
    ElMessage.error('取消匹配失败: ' + error.message)
  }
}

// 处理匹配弹窗关闭事件（点击X按钮）
const handleMatchDialogClose = () => {
  // 如果已经匹配成功，直接关闭弹窗，不需要额外处理
  // 因为 handleMatchSuccess 会处理后续的匹配成功弹窗显示
  if (matchingStatus.value === 'MATCHED') {
    isMatching.value = false
    return
  }

  // 如果还在匹配中，弹出确认对话框
  ElMessageBox.confirm(
    '确定要取消匹配吗？',
    '取消匹配',
    {
      confirmButtonText: '确定取消',
      cancelButtonText: '继续匹配',
      type: 'warning',
    }
  ).then(() => {
    // 用户确认取消
    cancelMatchRequest()
  }).catch(() => {
    // 用户选择继续匹配，重新打开弹窗
    isMatching.value = true
  })
}

// ==================== 匹配相关辅助方法 ====================

/**
 * 开始匹配计时器
 */
const startMatchingTimer = () => {
  matchingTimer = setInterval(() => {
    matchingTime.value++
  }, 1000)
}

/**
 * 停止匹配计时器
 */
const stopMatchingTimer = () => {
  if (matchingTimer) {
    clearInterval(matchingTimer)
    matchingTimer = null
  }
}

/**
 * 开始匹配状态轮询
 */
const startMatchStatusPolling = () => {
  const currentUser = userInfoStore.userInfo
  if (!currentUser) return

  let pollCount = 0
  const maxPolls = 150 // 最多轮询5分钟 (150 * 2秒)

  matchStatusTimer = setInterval(async () => {
    pollCount++

    // 检查是否超过最大轮询次数
    if (pollCount > maxPolls) {
      console.warn('⏰ 匹配轮询超时')
      stopMatchingTimer()
      stopMatchStatusPolling()
      isMatching.value = false
      matchingStatus.value = 'NOT_MATCHING'
      ElMessage.error('匹配超时，请重新开始匹配')
      return
    }

    try {
      const response = await getMatchStatus(currentUser.id)

      if (response.status && response.data) {
        const status = response.data

        matchingMessage.value = status.message || '正在寻找对手...'

        if (status.status === 'MATCHED') {
          // 匹配成功
          console.log('🎉 收到匹配成功状态:', status)
          handleMatchSuccess(status)
        } else if (status.status === 'TIMEOUT') {
          // 后端匹配超时
          console.log('⏰ 收到匹配超时状态')
          stopMatchingTimer()
          stopMatchStatusPolling()
          isMatching.value = false
          matchingStatus.value = 'NOT_MATCHING'
          ElMessage.error('匹配超时，请重新开始匹配')
        } else if (status.status === 'NOT_MATCHING') {
          // 匹配被取消或超时
          console.log('❌ 用户不在匹配队列中')
          stopMatchingTimer()
          stopMatchStatusPolling()
          isMatching.value = false
          matchingStatus.value = 'NOT_MATCHING'
          ElMessage.warning('匹配已取消或超时')
        } else {
          // 更新等待时间
          if (status.waitingTime) {
            matchingTime.value = status.waitingTime
          }
        }
      }

    } catch (error) {
      console.error('❌ 查询匹配状态失败:', error)
      // 网络错误不立即停止匹配，继续重试
    }
  }, 2000) // 每2秒查询一次
}

/**
 * 停止匹配状态轮询
 */
const stopMatchStatusPolling = () => {
  if (matchStatusTimer) {
    clearInterval(matchStatusTimer)
    matchStatusTimer = null
  }
}

/**
 * 处理匹配成功
 */
const handleMatchSuccess = (matchResult) => {
  console.log('🎉 匹配成功:', matchResult)

  // 停止计时器和轮询
  stopMatchingTimer()
  stopMatchStatusPolling()

  // 更新状态
  matchingStatus.value = 'MATCHED'

  // 保存匹配成功信息
  matchSuccessInfo.value = {
    opponent: matchResult.opponent || '未知对手',
    roomCode: matchResult.roomCode,
    roomInfo: matchResult.roomInfo
  }

  // 先关闭匹配弹窗，再显示匹配成功弹窗
  isMatching.value = false

  // 延迟一点显示匹配成功弹窗，确保匹配弹窗已关闭
  setTimeout(() => {
    showMatchSuccess.value = true
    ElMessage.success(`匹配成功！对手：${matchResult.opponent || '未知对手'}`)
  }, 100)

  // 开始5秒倒计时
  countdown.value = 5
  const countdownTimer = setInterval(() => {
    countdown.value--

    if (countdown.value <= 0) {
      clearInterval(countdownTimer)

      // 关闭匹配成功弹窗
      showMatchSuccess.value = false

      // 倒计时结束，进入房间
      if (matchSuccessInfo.value.roomInfo) {
        currentRoom.value = matchSuccessInfo.value.roomInfo

        // 匹配房间直接进入对战界面（后端已自动开始对战）
        showBattleRoom.value = true
        inBattle.value = true
        startBattleTimer()
        ElMessage.success('进入匹配对战！')
      } else if (matchSuccessInfo.value.roomCode) {
        // 如果只有房间码，获取房间详细信息
        handleRoomFound(matchSuccessInfo.value.roomCode)
      }

      // 重置状态
      matchingStatus.value = 'NOT_MATCHING'
      matchSuccessInfo.value = null
    }
  }, 1000)
}

// 立即进入房间
const enterBattleRoom = () => {
  // 关闭匹配成功弹窗
  showMatchSuccess.value = false

  // 立即进入房间
  if (matchSuccessInfo.value?.roomInfo) {
    currentRoom.value = matchSuccessInfo.value.roomInfo

    // 匹配房间直接进入对战界面（后端已自动开始对战）
    showBattleRoom.value = true
    inBattle.value = true
    startBattleTimer()
    ElMessage.success('进入匹配对战！')
  } else if (matchSuccessInfo.value?.roomCode) {
    // 如果只有房间码，获取房间详细信息
    handleRoomFound(matchSuccessInfo.value.roomCode)
  }

  // 重置状态
  matchingStatus.value = 'NOT_MATCHING'
  matchSuccessInfo.value = null
  countdown.value = 0
}

/**
 * 自动开始匹配对战
 */
const autoStartMatchBattle = async (roomInfo) => {
  try {
    console.log('🚀 自动开始匹配对战:', roomInfo)

    // 如果房间已经在对战中，直接进入对战界面
    if (roomInfo.status === 'BATTLING') {
      showBattleRoom.value = true
      inBattle.value = true
      startBattleTimer()
      ElMessage.success('进入匹配对战')
      return
    }

    // 如果房间状态是READY，自动开始对战
    if (roomInfo.status === 'READY') {
      const response = await startBattle(roomInfo.roomCode)

      if (response.status) {
        // 对战开始成功，进入对战界面
        showBattleRoom.value = true
        inBattle.value = true
        startBattleTimer()
        ElMessage.success('匹配对战开始！')
      } else {
        ElMessage.error('开始对战失败: ' + response.message)
        // 失败时显示房间等待界面
        showRoomWaiting.value = true
      }
    } else {
      // 其他状态，显示房间等待界面
      showRoomWaiting.value = true
    }

  } catch (error) {
    console.error('❌ 自动开始匹配对战失败:', error)
    ElMessage.error('开始对战失败: ' + error.message)
    // 失败时显示房间等待界面
    showRoomWaiting.value = true
  }
}

/**
 * 处理找到房间（通过房间码）
 */
const handleRoomFound = async (roomCode) => {
  try {
    const response = await getRoomInfo(roomCode)

    if (response.status && response.data) {
      const roomInfo = response.data
      currentRoom.value = roomInfo

      // 检查房间状态
      if (roomInfo.status === 'BATTLING') {
        // 房间已经在对战中，直接进入对战界面
        showBattleRoom.value = true
        inBattle.value = true
        startBattleTimer()
        ElMessage.success('进入对战房间')
      } else if (roomInfo.status === 'READY') {
        // 房间准备就绪，显示房间等待界面
        showRoomWaiting.value = true
        ElMessage.success(`成功进入房间 ${roomCode}`)
      } else {
        // 其他状态，显示房间等待界面
        showRoomWaiting.value = true
        ElMessage.success(`成功进入房间 ${roomCode}`)
      }
    } else {
      ElMessage.error('房间不存在或已关闭')
    }
  } catch (error) {
    console.error('❌ 获取房间信息失败:', error)
    ElMessage.error('获取房间信息失败: ' + error.message)
  }
}

// ==================== 用户房间状态检查 ====================

/**
 * 检查用户当前房间状态并自动显示房间弹窗
 */
const checkAndShowUserRoom = async () => {
  const currentUser = userInfoStore.userInfo
  if (!currentUser) {
    return
  }

  try {
    const response = await getCurrentRoomStatus()

    if (response.status && response.data && response.data.hasRoom) {
      const userRoomCode = response.data.roomCode
      console.log('📋 检测到用户在房间中:', userRoomCode)

      // 获取房间详细信息
      try {
        const roomResponse = await getRoomInfo(userRoomCode)

        if (roomResponse.status && roomResponse.data) {
          currentRoom.value = roomResponse.data
          console.log('🏠 自动显示房间弹窗:', roomResponse.data)

          // 根据房间状态显示相应界面
          switch (roomResponse.data.status) {
            case 'WAITING':
            case 'READY':
              showRoomWaiting.value = true
              showBattleRoom.value = false
              break
            case 'BATTLING':
              showRoomWaiting.value = false
              showBattleRoom.value = true
              break
            default:
              console.log('房间状态异常:', roomResponse.data.status)
          }
        } else {
          console.log('❌ 房间信息获取失败')
        }

      } catch (roomError) {
        console.error('❌ 获取房间信息失败:', roomError)
      }
    } else {
      console.log('📋 用户当前没有房间')
    }

  } catch (error) {
    console.error('❌ 检查用户房间状态失败:', error)
  }
}

// ==================== 房间管理方法 ====================

/**
 * 打开创建房间弹窗
 */
const openCreateRoom = () => {
  if (roomManagerRef.value) {
    roomManagerRef.value.openCreateDialog()
  }
}

/**
 * 打开加入房间弹窗
 */
const openJoinRoom = () => {
  if (roomManagerRef.value) {
    roomManagerRef.value.openJoinDialog()
  }
}

/**
 * 打开房间列表弹窗
 */
const openRoomList = () => {
  if (roomManagerRef.value) {
    roomManagerRef.value.openRoomListDialog()
  }
}

/**
 * 处理房间创建成功事件
 *
 * @param {Object} roomInfo 创建成功的房间信息
 */
const handleRoomCreated = (roomInfo) => {
  console.log('🏠 房间创建成功:', roomInfo)
  currentRoom.value = roomInfo

  // 显示房间码给用户
  ElMessage({
    message: `房间创建成功！房间码: ${roomInfo.roomCode}`,
    type: 'success',
    duration: 5000
  })

  // 直接显示房间等待界面
  showRoomWaiting.value = true
  showBattleRoom.value = false
}

/**
 * 处理加入房间成功事件
 *
 * @param {Object} roomInfo 加入成功的房间信息
 */
const handleRoomJoined = (roomInfo) => {
  console.log('🚪 成功加入房间:', roomInfo)
  currentRoom.value = roomInfo

  ElMessage.success(`成功加入房间 ${roomInfo.roomCode}`)

  // 直接显示房间等待界面
  showRoomWaiting.value = true
  showBattleRoom.value = false
}

/**
 * 处理房间操作错误事件
 *
 * @param {string} errorMessage 错误信息
 */
const handleRoomError = (errorMessage) => {
  console.error('❌ 房间操作失败:', errorMessage)
  ElMessage.error(errorMessage)
}

/**
 * 处理房间等待界面关闭事件
 */
const handleRoomWaitingClose = () => {
  showRoomWaiting.value = false
  currentRoom.value = null
}

/**
 * 处理对战开始事件
 *
 * @param {Object} roomInfo 房间信息
 */
const handleBattleStart = (roomInfo) => {
  console.log('⚔️ 对战开始:', roomInfo)
  currentRoom.value = roomInfo

  // 关闭房间等待界面
  showRoomWaiting.value = false

  // 进入对战界面
  showBattleRoom.value = true
  inBattle.value = true
  startBattleTimer()

  ElMessage.success('对战开始！')
}

/**
 * 处理房间信息更新事件
 *
 * @param {Object} roomInfo 更新后的房间信息
 */
const handleRoomUpdate = (roomInfo) => {
  console.log('🔄 房间信息更新:', roomInfo)
  currentRoom.value = roomInfo
}

/**
 * 处理对战结束事件
 *
 * @param {Object} battleResult 对战结果
 */
const handleBattleEnd = (battleResult) => {
  console.log('🏁 对战结束:', battleResult)

  // 停止计时器
  stopBattleTimer()

  // 关闭对战界面
  showBattleRoom.value = false
  inBattle.value = false

  // 显示结果
  const isWinner = battleResult.winner && battleResult.winner.id === userInfoStore.userInfo.id
  const reason = battleResult.reason || 'UNKNOWN'

  let resultMessage = ''
  let messageType = 'info'

  if (isWinner) {
    switch (reason) {
      case 'SOLVED':
        resultMessage = '🎉 恭喜！您成功解决了题目，获得胜利！'
        messageType = 'success'
        break
      case 'SURRENDER':
      case 'QUIT':
        resultMessage = '🎉 对手放弃了对战，您获得胜利！'
        messageType = 'success'
        break
      default:
        resultMessage = '🎉 恭喜您获得胜利！'
        messageType = 'success'
    }
  } else {
    switch (reason) {
      case 'SOLVED':
        resultMessage = '😔 对手率先解决了题目，您败北了！'
        messageType = 'warning'
        break
      case 'SURRENDER':
        resultMessage = '😔 您主动放弃了对战，判定为失败！'
        messageType = 'warning'
        break
      case 'QUIT':
        resultMessage = '😔 您退出了对战，判定为失败！'
        messageType = 'warning'
        break
      default:
        resultMessage = '😔 很遗憾，您败北了！'
        messageType = 'warning'
    }
  }

  ElMessage({
    message: resultMessage,
    type: messageType,
    duration: 8000,
    showClose: true
  })

  // 重置状态
  currentRoom.value = null
}

/**
 * 处理对战界面关闭事件
 */
const handleBattleRoomClose = () => {
  showBattleRoom.value = false
  inBattle.value = false
  stopBattleTimer()
  currentRoom.value = null
}

const startBattleTimer = () => {
  battleTime.value = 0
  battleTimer = setInterval(() => {
    battleTime.value++
  }, 1000)
}

const stopBattleTimer = () => {
  if (battleTimer) {
    clearInterval(battleTimer)
    battleTimer = null
  }
}

const runCode = () => {
  // 运行代码逻辑
  runResult.value = {
    status: 'success',
    details: '测试用例通过'
  }
}

const submitCode = () => {
  // 提交代码逻辑
  clearInterval(battleTimer)
  showResultDialog.value = true
}

const getDifficultyType = (difficulty) => {
  const types = {
    'Easy': 'success',
    'Medium': 'warning',
    'Hard': 'danger'
  }
  return types[difficulty] || 'info'
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const backToSelection = () => {
  showResultDialog.value = false
  inBattle.value = false
  battleTime.value = 0
  userCode.value = ''
  runResult.value = null
}

const startNewBattle = () => {
  showResultDialog.value = false
  startRandomMatch()
}

onMounted(() => {
  // 检查用户当前房间状态并自动显示房间弹窗
  checkAndShowUserRoom()
})

onUnmounted(() => {
  // 清理所有定时器
  stopMatchingTimer()
  stopMatchStatusPolling()
  if (battleTimer) clearInterval(battleTimer)

  // 如果正在匹配，取消匹配
  if (isMatching.value) {
    cancelMatchRequest()
  }
})
</script>

<style lang="scss" scoped>
/* ==================== 基础样式 ==================== */

.battle-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ==================== 模式选择样式 ==================== */

.mode-selection {
  padding: 40px 20px;
}

.mode-header {
  text-align: center;
  margin-bottom: 50px;
}

.mode-header h1 {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin: 0 0 15px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mode-header p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mode-cards {
  margin-top: 40px;
}

.mode-card {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  height: 100%;
  min-height: 400px;
}

.mode-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.mode-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.random-match .mode-card-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.room-battle .mode-card-bg {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.mode-card:hover .mode-card-bg {
  opacity: 0.1;
}

.mode-content {
  position: relative;
  z-index: 2;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.mode-icon-wrapper {
  margin-bottom: 20px;
}

.mode-icon {
  font-size: 64px;
  color: #667eea;
  transition: all 0.3s ease;
}

.room-battle .mode-icon {
  color: #f5576c;
}

.mode-card:hover .mode-icon {
  transform: scale(1.1);
}

.mode-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 15px 0;
}

.mode-content > p {
  font-size: 16px;
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.6;
}

.mode-features {
  list-style: none;
  padding: 0;
  margin: 20px 0;
  text-align: left;
}

.mode-features li {
  font-size: 14px;
  color: #666;
  margin: 8px 0;
  padding-left: 10px;
}

.mode-button {
  margin-top: auto;
}

.mode-button .el-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}

.room-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.room-buttons .el-button {
  height: 45px;
  font-size: 14px;
  font-weight: 500;
}

/* ==================== 匹配弹窗样式 ==================== */

.matching-dialog {
  --el-dialog-border-radius: 12px;
}

.matching-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.matching-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.matching-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 18px;
}

.matching-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #f0f0f0;
}

.matching-content {
  text-align: center;
  padding: 30px 20px 20px;
}

.matching-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #667eea;

  &.rotating {
    animation: rotate 2s linear infinite;
  }
}

.matching-content h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.matching-time {
  font-size: 16px;
  color: #666;
  margin: 10px 0 20px 0;
  font-weight: 500;
}

.matching-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.matching-tips p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: center;
  padding: 10px 0;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ==================== 匹配成功弹窗样式 ==================== */

.match-success-dialog {
  --el-dialog-border-radius: 16px;
}

.match-success-dialog .el-dialog__header {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
  border-radius: 16px 16px 0 0;
  padding: 24px;
  text-align: center;
}

.match-success-dialog .el-dialog__title {
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.match-success-content {
  text-align: center;
  padding: 30px 20px;
}

.success-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 20px;

  &.bounce {
    animation: bounce 1s ease-in-out;
  }
}

.match-success-content h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.opponent-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
}

.opponent-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.opponent-avatar .el-icon {
  font-size: 30px;
  color: white;
}

.opponent-info h3 {
  margin: 0 0 10px 0;
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
}

.opponent-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.countdown-display {
  margin: 25px 0;
}

.countdown-display p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}

.countdown-circle {
  width: 80px;
  height: 80px;
  border: 4px solid #52c41a;
  border-radius: 50%;
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.countdown-number {
  font-size: 28px;
  font-weight: 700;
  color: #52c41a;
  z-index: 2;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.battle-arena {
  .battle-info {
    margin-bottom: 20px;

    .player-info {
      display: flex;
      align-items: center;

      &.opponent {
        justify-content: flex-end;

        .player-details {
          text-align: right;
          margin-right: 10px;
        }
      }

      .player-details {
        margin-left: 10px;

        .player-name {
          font-weight: bold;
        }

        .player-rating {
          color: #666;
          font-size: 0.9em;
        }
      }
    }

    .battle-center {
      text-align: center;

      .timer {
        font-size: 1.5em;
        font-weight: bold;
        color: #409EFF;
      }

      .problem-title {
        margin-top: 5px;
        color: #666;
      }
    }
  }

  .battle-main {
    .problem-card {
      height: 600px;
      overflow-y: auto;

      .problem-content {
        .problem-difficulty {
          margin: 10px 0;
        }

        .problem-description {
          margin: 15px 0;
          line-height: 1.6;
        }

        .problem-examples {
          .example {
            margin: 10px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;

            .example-input,
            .example-output {
              margin: 5px 0;

              pre {
                margin: 5px 0;
                padding: 5px;
                background-color: #fff;
                border: 1px solid #ddd;
                border-radius: 3px;
              }
            }
          }
        }
      }
    }

    .code-card {
      .editor-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .el-select {
          width: 120px;
        }
    }

      .code-textarea {
        font-family: 'Courier New', monospace;
      }
    }

    .result-card {
      margin-top: 20px;

      .result-content {
        .result-status {
          font-weight: bold;
          margin-bottom: 10px;

          &.success {
            color: #67C23A;
          }

          &.error {
            color: #F56C6C;
          }
        }
      }
    }
  }
}

.battle-result {
  text-align: center;

  .result-icon {
    font-size: 4em;
    margin-bottom: 20px;

    .win-icon {
      color: #67C23A;
    }

    .lose-icon {
      color: #F56C6C;
    }
  }

  .rating-change {
    margin: 15px 0;
    font-size: 1.2em;

    .rating-up {
      color: #67C23A;
      font-weight: bold;
      }
      
      .rating-down {
        color: #F56C6C;
        font-weight: bold;
      }
    }
    
    .battle-stats {
      margin-top: 20px;
      color: #666;

      p {
        margin: 5px 0;
      }
    }
  }

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 匹配成功样式 */
.match-success-card {
  text-align: center;
  margin: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.match-success-content {
  padding: 40px 20px;
}

.success-animation {
  margin-bottom: 20px;
}

.success-icon {
  font-size: 64px;
  color: #67c23a;
  animation: bounce 1s ease-in-out infinite alternate;
}

@keyframes bounce {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-10px);
  }
}

.opponent-info {
  margin: 20px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.opponent-info h3 {
  margin: 0;
  font-size: 24px;
  color: #ffd700;
}

.countdown-display {
  margin-top: 30px;
}

.countdown-display p {
  font-size: 18px;
  margin-bottom: 15px;
}

.countdown-circle {
  display: inline-block;
  width: 80px;
  height: 80px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  line-height: 72px;
  font-size: 32px;
  font-weight: bold;
  color: #ffd700;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
  }
}
</style>
