package com.xju.codeduel.model.dto;

import com.xju.codeduel.model.domain.ChatMessages;
import com.xju.codeduel.model.domain.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "MessageWithUserDTO对象", description = "消息及关联用户信息")
public class MessageWithUserDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息内容")
    private ChatMessages message;

    @ApiModelProperty(value = "发送用户信息")
    private Users user;
}