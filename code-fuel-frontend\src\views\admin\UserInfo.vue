<!--
  用户基本资料页面

  功能说明：
  1. 显示用户的基本信息（用户名、Rating、权限、状态等）
  2. 支持头像上传和更新
  3. 支持密码修改
  4. 数据同步机制：
     - 页面加载时从后端Session获取最新用户信息
     - 头像/密码更新后，后端会同时更新数据库和Session
     - 前端重新加载用户信息，确保数据一致性

  数据流向：
  前端更新 → 后端updateUser接口 → 更新数据库 + 更新Session → 前端重新加载 → UI同步更新
-->

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Edit,
  Key,
  Calendar,
  Trophy,
  Setting,
  Camera,
  Plus
} from '@element-plus/icons-vue'
import { useUserInfoStore } from '@/stores/userInfo'
import { updateUser, getInfo } from '@/api/api'

// 用户信息store
const userInfoStore = useUserInfoStore()

// 响应式数据
const userInfo = ref({})
const loading = ref(false)
const passwordDialogVisible = ref(false)
const avatarUploading = ref(false)

// 密码修改表单
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

// 初始化用户信息
onMounted(() => {
  loadUserInfo()
})

/**
 * 加载用户信息
 *
 * 从后端获取最新的用户信息并同步到本地
 * 这个函数会：
 * 1. 调用getInfo接口从后端Session获取用户信息
 * 2. 更新本地userInfo变量（用于基本资料页面显示）
 * 3. 更新userInfoStore（用于导航栏等全局组件显示）
 */
const loadUserInfo = async () => {
  try {
    // 从后端获取最新的用户信息（来自Session）
    const response = await getInfo()
    if (response.data) {
      // 更新本地变量，用于当前页面显示
      userInfo.value = { ...response.data }
      // 同时更新全局store，确保导航栏等组件也能获取最新数据
      userInfoStore.setUserInfo(response.data)
    } else {
      // 如果后端获取失败，使用store中的数据作为备用
      userInfo.value = { ...userInfoStore.userInfo }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 出错时使用store中的数据作为备用
    userInfo.value = { ...userInfoStore.userInfo }
  }
}

/**
 * 头像上传成功后的处理函数
 *
 * 处理流程：
 * 1. 解析上传响应，获取新头像URL
 * 2. 构建用户更新数据（包含完整信息，避免字段丢失）
 * 3. 调用后端updateUser接口（后端会同时更新数据库和Session）
 * 4. 重新加载用户信息，确保前端数据与后端同步
 *
 * @param {Object} response 头像上传接口的响应数据
 */
const handleAvatarSuccess = async (response) => {
  try {
    avatarUploading.value = false

    // 解析头像URL，兼容不同的响应格式
    let newAvatarUrl = null
    if (response && response.data) {
      if (typeof response.data === 'string') {
        // 直接返回文件路径字符串
        newAvatarUrl = response.data
      } else if (response.data.url) {
        // 返回对象包含url字段
        newAvatarUrl = response.data.url
      } else if (response.data.filePath) {
        // 返回对象包含filePath字段
        newAvatarUrl = response.data.filePath
      }
    }

    if (newAvatarUrl) {
      // 构建更新数据：包含完整的用户信息，只更新头像字段
      // 这样做是为了避免MyBatis-Plus在部分更新时可能出现的字段丢失问题
      const updateData = {
        id: userInfo.value.id,
        codeforcesId: userInfo.value.codeforcesId,
        avatar: newAvatarUrl, // 新头像URL
        rating: userInfo.value.rating,
        isAdmin: userInfo.value.isAdmin,
        status: userInfo.value.status,
        banReason: userInfo.value.banReason,
        registerTime: userInfo.value.registerTime,
        lastLogin: userInfo.value.lastLogin,
        currentRoomCode: userInfo.value.currentRoomCode,
        roomStatus: userInfo.value.roomStatus,
        updateTime: userInfo.value.updateTime
        // 注意：不包含password字段，避免意外覆盖密码
      }

      // 调用后端更新接口
      // 后端会同时更新数据库和Session中的用户信息
      const updateResponse = await updateUser(updateData)

      if (updateResponse.status) {
        ElMessage.success('头像更新成功')

        // 重新从后端获取最新的用户信息
        // 这样确保本地数据与后端Session中的数据完全同步
        // 同时也会更新userInfoStore，使导航栏头像也能同步更新
        await loadUserInfo()
      } else {
        ElMessage.error('头像更新失败: ' + (updateResponse.message || '未知错误'))
      }
    } else {
      ElMessage.error('头像上传失败：无法获取文件URL')
    }
  } catch (error) {
    console.error('头像更新失败:', error)
    ElMessage.error('头像更新失败: ' + (error.response?.data?.message || error.message))
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
    return false
  }

  avatarUploading.value = true
  return true
}

// 显示密码修改对话框
const showPasswordDialog = () => {
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  }
  passwordDialogVisible.value = true
}

/**
 * 提交密码修改
 *
 * 处理流程：
 * 1. 验证密码输入（一致性、长度等）
 * 2. 构建更新数据（只包含必要字段）
 * 3. 调用后端updateUser接口
 * 4. 后端会自动加密密码并更新Session
 */
const submitPassword = async () => {
  // 验证两次密码输入是否一致
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }

  // 验证密码长度
  if (passwordForm.value.newPassword.length < 6) {
    ElMessage.error('密码长度不能少于6位')
    return
  }

  try {
    // 构建更新数据：只包含必要字段，避免意外覆盖其他信息
    const updateData = {
      id: userInfo.value.id,
      codeforcesId: userInfo.value.codeforcesId,
      password: passwordForm.value.newPassword // 后端会自动加密
    }

    // 调用更新接口，后端会同时更新数据库和Session
    await updateUser(updateData)
    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
  } catch (error) {
    ElMessage.error('密码修改失败')
  }
}

// 获取用户状态文本
const getStatusText = (status) => {
  return status === 1 ? '已封禁' : '正常'
}

// 获取用户状态标签类型
const getStatusTagType = (status) => {
  return status === 1 ? 'danger' : 'success'
}

// 获取权限文本
const getAdminText = (isAdmin) => {
  if (isAdmin === 2) return '超级管理员'
  if (isAdmin === 1) return '管理员'
  return '普通用户'
}

// 获取权限标签类型
const getAdminTagType = (isAdmin) => {
  if (isAdmin === 2) return 'danger'
  if (isAdmin === 1) return 'warning'
  return 'info'
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '未知'
  return new Date(timeString).toLocaleString('zh-CN')
}
</script>

<template>
  <div class="user-info-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <div class="header-content">
        <h2>
          <el-icon><User /></el-icon>
          个人资料
        </h2>
      </div>
    </el-card>

    <!-- 用户信息卡片 -->
    <el-card class="info-card">
      <div class="user-profile">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <el-upload
                class="avatar-uploader"
                action="/api/file/upload"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
            >
              <img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar" />
              <div v-else class="avatar-placeholder">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="avatar-overlay" v-loading="avatarUploading">
                <el-icon><Camera /></el-icon>
                <span>点击更换头像</span>
              </div>
            </el-upload>
          </div>
          <p class="avatar-tip">点击头像可以更换</p>
        </div>

        <!-- 基本信息区域 -->
        <div class="info-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">
                <el-icon><User /></el-icon>
                用户名
              </div>
              <div class="info-value">{{ userInfo.codeforcesId || '-' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <el-icon><Trophy /></el-icon>
                Rating
              </div>
              <div class="info-value">
                <el-tag type="warning" size="large">{{ userInfo.rating || 1500 }}</el-tag>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <el-icon><Setting /></el-icon>
                权限
              </div>
              <div class="info-value">
                <el-tag :type="getAdminTagType(userInfo.isAdmin)" size="large">
                  {{ getAdminText(userInfo.isAdmin) }}
                </el-tag>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">状态</div>
              <div class="info-value">
                <el-tag :type="getStatusTagType(userInfo.status)" size="large">
                  {{ getStatusText(userInfo.status) }}
                </el-tag>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <el-icon><Calendar /></el-icon>
                注册时间
              </div>
              <div class="info-value">{{ formatTime(userInfo.registerTime) }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">最后登录</div>
              <div class="info-value">{{ formatTime(userInfo.lastLogin) || '从未登录' }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="action-card">
      <template #header>
        <span>账户操作</span>
      </template>

      <div class="action-buttons">
        <el-button type="primary" size="large" @click="showPasswordDialog">
          <el-icon><Key /></el-icon>
          修改密码
        </el-button>

        <el-button type="info" size="large" @click="loadUserInfo">
          <el-icon><Edit /></el-icon>
          刷新信息
        </el-button>
      </div>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="400px"
    >
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="新密码">
          <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
          />
        </el-form-item>

        <el-form-item label="确认密码">
          <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPassword">
            <el-icon><Key /></el-icon>
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.user-info-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .header-content {
      h2 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
        color: #303133;
        font-size: 24px;

        .el-icon {
          color: #409EFF;
        }
      }
    }
  }

  .info-card {
    margin-bottom: 20px;

    .user-profile {
      display: flex;
      gap: 40px;

      .avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;

        .avatar-container {
          position: relative;

          .avatar-uploader {
            .el-upload {
              position: relative;
              border: 2px dashed #d9d9d9;
              border-radius: 50%;
              cursor: pointer;
              overflow: hidden;
              transition: all 0.3s ease;
              width: 120px;
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                border-color: #409EFF;

                .avatar-overlay {
                  opacity: 1;
                }
              }
            }

            .avatar {
              width: 120px;
              height: 120px;
              border-radius: 50%;
              object-fit: cover;
            }

            .avatar-placeholder {
              width: 120px;
              height: 120px;
              border-radius: 50%;
              background-color: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28px;
              color: #c0c4cc;
            }

            .avatar-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.6);
              color: white;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: opacity 0.3s ease;
              border-radius: 50%;
              font-size: 12px;

              .el-icon {
                font-size: 20px;
                margin-bottom: 4px;
              }
            }
          }
        }

        .avatar-tip {
          margin-top: 10px;
          font-size: 12px;
          color: #909399;
          text-align: center;
        }
      }

      .info-section {
        flex: 1;

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;

          .info-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409EFF;

            .info-label {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
              font-weight: 500;

              .el-icon {
                color: #409EFF;
              }
            }

            .info-value {
              font-size: 16px;
              color: #303133;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .action-card {
    .action-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;

      .el-button {
        padding: 12px 24px;
        font-size: 14px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

// 对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .user-info-container {
    padding: 15px;

    .info-card .user-profile {
      flex-direction: column;
      gap: 20px;
      text-align: center;

      .info-section .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }

    .action-card .action-buttons {
      flex-direction: column;
      align-items: center;

      .el-button {
        width: 200px;
      }
    }
  }
}
</style>
