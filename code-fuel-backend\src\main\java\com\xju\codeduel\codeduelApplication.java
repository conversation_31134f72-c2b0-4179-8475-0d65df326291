package com.xju.codeduel;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.PrintStream;
import java.nio.charset.StandardCharsets;

@SpringBootApplication
@MapperScan({"com.xju.codeduel.mapper"})
@EnableAsync
public class codeduelApplication {
    public static void main(String[] args) {
        System.setOut(new PrintStream(System.out, true, StandardCharsets.UTF_8));
        SpringApplication.run(codeduelApplication.class, args);
    }

}
