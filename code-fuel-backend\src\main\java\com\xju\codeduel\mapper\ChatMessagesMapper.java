package com.xju.codeduel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xju.codeduel.model.domain.ChatMessages;
import com.xju.codeduel.model.dto.MessageWithUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 聊天消息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface ChatMessagesMapper extends BaseMapper<ChatMessages> {

    /**
     * 查询消息及关联用户信息
     * @param codeforcesId 可选参数，筛选特定用户的消息
     * @return 消息及用户信息列表
     */
    List<MessageWithUserDTO> selectMessagesWithUsers(@Param("codeforcesId") String codeforcesId);

}
