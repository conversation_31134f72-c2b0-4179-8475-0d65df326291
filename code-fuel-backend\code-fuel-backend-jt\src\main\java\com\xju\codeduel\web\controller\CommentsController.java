package com.xju.codeduel.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.CommentDTO;
import com.xju.codeduel.model.dto.PageDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.ICommentsService;
import com.xju.codeduel.service.IPostsService;
import com.xju.codeduel.model.domain.Comments;
import com.xju.codeduel.model.domain.Posts;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/comments")
public class CommentsController {

    private final Logger logger = LoggerFactory.getLogger( CommentsController.class );

    @Autowired
    private ICommentsService commentsService;

    @Autowired
    private IPostsService postsService;


    /**
     * 描述：根据Id 查询
     *
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Comments> getById(@PathVariable("id") Long id)throws Exception {
        Comments comments = commentsService.getById(id);
        return JsonResponse.success(comments);
    }


    /**
     * 分页查询帖子下的评论
     * @param postId 帖子ID
     * @param pageNum 页码（默认1）
     * @param pageSize 每页大小（默认10）
     * @return 评论分页结果
     */
    @GetMapping("/post/{postId}")
    @ApiOperation(value = "分页查询帖子评论", notes = "分页获取指定帖子下的所有评论信息，按时间升序排列")
    public JsonResponse<Page<CommentDTO>> getCommentsByPostId(
            @ApiParam(value = "帖子ID", required = true)
            @PathVariable("postId") Integer postId,
            @ApiParam(value = "页码", defaultValue = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        try {
            logger.info("分页查询帖子评论，帖子ID: {}, 页码: {}, 每页大小: {}", postId, pageNum, pageSize);

            // 创建分页参数
            PageDTO pageDTO = new PageDTO();
            pageDTO.setPageNo(pageNum);
            pageDTO.setPageSize(pageSize);

            // 执行分页查询
            Page<CommentDTO> result = commentsService.getCommentsByPostId(pageDTO, postId);

            logger.info("成功获取帖子评论，帖子ID: {}, 总记录数: {}, 当前页记录数: {}",
                    postId, result.getTotal(), result.getRecords().size());

            return JsonResponse.success(result);

        } catch (IllegalArgumentException e) {
            logger.warn("参数错误: {}", e.getMessage());
            return JsonResponse.failure("参数错误: " + e.getMessage());

        } catch (Exception e) {
            logger.error("分页查询帖子评论失败，帖子ID: {}", postId, e);
            return JsonResponse.failure("获取评论失败: " + e.getMessage());
        }
    }

    /**
     * 查询帖子下的所有评论（不分页，用于前端一次性加载所有评论）
     * @param postId 帖子ID
     * @return 所有评论列表
     */
    @GetMapping("/post/{postId}/all")
    @ApiOperation(value = "获取帖子所有评论", notes = "获取指定帖子下的所有评论信息，不分页")
    public JsonResponse<Page<CommentDTO>> getAllCommentsByPostId(
            @ApiParam(value = "帖子ID", required = true)
            @PathVariable("postId") Integer postId) {

        try {
            logger.info("获取帖子所有评论，帖子ID: {}", postId);

            // 设置一个较大的页面大小来获取所有评论
            PageDTO pageDTO = new PageDTO();
            pageDTO.setPageNo(1);
            pageDTO.setPageSize(1000); // 假设单个帖子评论不会超过1000条

            Page<CommentDTO> result = commentsService.getCommentsByPostId(pageDTO, postId);

            logger.info("成功获取帖子所有评论，帖子ID: {}, 评论总数: {}", postId, result.getTotal());

            return JsonResponse.success(result);

        } catch (IllegalArgumentException e) {
            logger.warn("参数错误: {}", e.getMessage());
            return JsonResponse.failure("参数错误: " + e.getMessage());

        } catch (Exception e) {
            logger.error("获取帖子所有评论失败，帖子ID: {}", postId, e);
            return JsonResponse.failure("获取评论失败: " + e.getMessage());
        }
    }


    /**
     * 创建新评论
     * @param comment 评论对象
     * @return 创建结果
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建新评论", notes = "创建新的评论或回复")
    public JsonResponse<Comments> createComment(@RequestBody Comments comment) {
        try {
            logger.info("创建新评论，帖子ID: {}, 用户ID: {}", comment.getPostId(), comment.getUserId());

            // 参数校验
            if (comment.getPostId() == null) {
                return JsonResponse.failure("帖子ID不能为空");
            }
            if (comment.getUserId() == null) {
                return JsonResponse.failure("用户ID不能为空");
            }
            if (comment.getContent() == null || comment.getContent().trim().isEmpty()) {
                return JsonResponse.failure("评论内容不能为空");
            }
            // 设置默认值
            comment.setCreateTime(java.time.LocalDateTime.now());
            comment.setIsDeleted(0);
            // 保存评论
            boolean success = commentsService.save(comment);

            if (success) {
                logger.info("评论创建成功，评论ID: {}", comment.getId());

                // 更新帖子的评论数（存储在like_count字段中）
                try {
                    Posts post = postsService.getById(comment.getPostId());
                    if (post != null) {
                        post.setCommentCount(post.getCommentCount()+ 1);
                        postsService.updateById(post);
                        logger.info("帖子评论数更新成功，帖子ID: {}, 新评论数: {}", post.getId(), post.getCommentCount());
                    }
                } catch (Exception e) {
                    logger.error("更新帖子评论数失败，帖子ID: {}", comment.getPostId(), e);
                    // 不影响评论创建的成功，只记录错误
                }

                return JsonResponse.success(comment);
            } else {
                logger.error("评论创建失败");
                return JsonResponse.failure("评论创建失败");
            }
        } catch (Exception e) {
            logger.error("创建评论失败", e);
            return JsonResponse.failure("创建评论失败: " + e.getMessage());
        }
    }

    /**
     * 删除评论
     * @param commentId 评论ID
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 删除结果
     */
    @DeleteMapping("/{commentId}")
    @ApiOperation(value = "删除评论", notes = "管理员可以删除任意评论，普通用户只能删除自己的评论")
    public JsonResponse<Boolean> deleteComment(
            @ApiParam(value = "评论ID", required = true)
            @PathVariable("commentId") Long commentId,
            @ApiParam(value = "当前用户ID", required = true)
            @RequestParam("userId") Long userId,
            @ApiParam(value = "是否为管理员", defaultValue = "false")
            @RequestParam(value = "isAdmin", defaultValue = "false") Boolean isAdmin) {

        try {
            logger.info("删除评论请求，评论ID: {}, 用户ID: {}, 是否管理员: {}", commentId, userId, isAdmin);

            // 参数校验
            if (commentId == null) {
                return JsonResponse.failure("评论ID不能为空");
            }
            if (userId == null) {
                return JsonResponse.failure("用户ID不能为空");
            }

            // 查询评论是否存在
            Comments comment = commentsService.getById(commentId);
            if (comment == null) {
                return JsonResponse.failure("评论不存在");
            }

            // 权限检查：管理员可以删除任意评论，普通用户只能删除自己的评论
            if (!isAdmin && !comment.getUserId().equals(userId)) {
                return JsonResponse.failure("无权限删除此评论");
            }

            boolean success = commentsService.removeById(commentId);

            if (success) {
                logger.info("评论删除成功，评论ID: {}", commentId);

                // 更新帖子的评论数（存储在like_count字段中）
                try {
                    Posts post = postsService.getById(comment.getPostId());
                    if (post != null && post.getCommentCount() > 0) {
                        post.setCommentCount(post.getCommentCount() - 1);
                        postsService.updateById(post);
                        logger.info("帖子评论数更新成功，帖子ID: {}, 新评论数: {}", post.getId(), post.getCommentCount());
                    }
                } catch (Exception e) {
                    logger.error("更新帖子评论数失败，帖子ID: {}", comment.getPostId(), e);
                    // 不影响评论删除的成功，只记录错误
                }

                return JsonResponse.success(true);
            } else {
                logger.error("评论删除失败，评论ID: {}", commentId);
                return JsonResponse.failure("删除评论失败");
            }

        } catch (Exception e) {
            logger.error("删除评论失败，评论ID: {}", commentId, e);
            return JsonResponse.failure("删除评论失败: " + e.getMessage());
        }
    }

}

