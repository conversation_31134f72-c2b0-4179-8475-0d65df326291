package com.xju.codeduel.service;

import com.xju.codeduel.model.dto.CodeforcesUserInfo;

/**
 * 验证码服务接口
 * 用于生成和验证Codeforces身份验证码
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IVerificationService {

    /**
     * 生成验证字符串
     * 生成一个复杂的随机字符串用于Codeforces身份验证
     * 
     * @return 验证字符串
     */
    String generateVerificationCode();

    /**
     * 验证Codeforces用户身份
     * 通过调用Codeforces API验证用户是否已将firstName设置为验证字符串
     *
     * @param codeforcesId 用户的Codeforces ID
     * @param verificationString 验证字符串
     * @return 验证是否成功
     */
    boolean verifyCodeforcesUser(String codeforcesId, String verificationString);

    /**
     * 获取Codeforces用户的firstName
     * 通过调用Codeforces API获取用户的firstName字段
     *
     * @param codeforcesId 用户的Codeforces ID
     * @return 用户的firstName，如果获取失败返回null
     */
    String getCodeforcesUserFirstName(String codeforcesId);

    /**
     * 获取Codeforces用户的完整信息
     * 通过调用Python服务获取用户的完整信息，包括头像等
     *
     * @param codeforcesId 用户的Codeforces ID
     * @return 用户的完整信息，如果获取失败返回null
     */
    CodeforcesUserInfo getCodeforcesUserInfo(String codeforcesId);
}
