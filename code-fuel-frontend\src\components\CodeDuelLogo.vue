<template>
  <!-- Logo容器，支持自定义宽高 -->
  <div class="logo-container" :style="{ width: width + 'px', height: height + 'px' }">
    <svg
      :width="width"
      :height="height"
      :viewBox="computedViewBox"
      xmlns="http://www.w3.org/2000/svg"
      class="codeduel-logo"
    >
      <!-- SVG渐变和滤镜定义区域 -->
      <defs>
        <!-- 主要渐变色：系统主蓝色，用于括号和交叉点 -->
        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#409EFF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#337ecc;stop-opacity:1" />
        </linearGradient>

        <!-- 强调渐变色：紫色系，用于对战剑和Duel文字 -->
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>

        <!-- 代码装饰点渐变色：混合色系 -->
        <linearGradient id="codeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#409EFF;stop-opacity:0.8" />
          <stop offset="50%" style="stop-color:#667eea;stop-opacity:0.9" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.8" />
        </linearGradient>

        <!-- 发光效果滤镜：用于悬停时的发光动画 -->
        <filter id="glow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      <!-- 主要图标区域：包含代码括号、对战剑、装饰元素 -->
      <g class="logo-icon">
        <!-- 左侧代码括号：象征编程元素 -->
        <path
          d="M 15 15 L 8 15 Q 5 15 5 18 L 5 42 Q 5 45 8 45 L 15 45"
          stroke="url(#primaryGradient)"
          stroke-width="2.5"
          fill="none"
          stroke-linecap="round"
          class="bracket-left"
        />

        <!-- 右侧代码括号：与左侧对称 -->
        <path
          d="M 45 15 L 52 15 Q 55 15 55 18 L 55 42 Q 55 45 52 45 L 45 45"
          stroke="url(#primaryGradient)"
          stroke-width="2.5"
          fill="none"
          stroke-linecap="round"
          class="bracket-right"
        />

        <!-- 中间对战符号：交叉的剑代表代码对战 -->
        <g class="battle-swords">
          <!-- 第一把剑：从左下到右上 -->
          <line x1="23" y1="37" x2="37" y2="23" stroke="url(#accentGradient)" stroke-width="2.2" stroke-linecap="round" class="sword1"/>
          <circle cx="22" cy="38" r="2.2" fill="url(#accentGradient)" class="sword1-handle"/>

          <!-- 第二把剑：从右下到左上，与第一把剑交叉 -->
          <line x1="37" y1="37" x2="23" y2="23" stroke="url(#accentGradient)" stroke-width="2.2" stroke-linecap="round" class="sword2"/>
          <circle cx="38" cy="38" r="2.2" fill="url(#accentGradient)" class="sword2-handle"/>

          <!-- 交叉点发光效果：突出对战的激烈感 -->
          <circle cx="30" cy="30" r="2.5" fill="url(#primaryGradient)" opacity="0.7" filter="url(#glow)" class="cross-glow"/>

          <!-- 火花效果：增强对战氛围 -->
          <circle cx="28" cy="28" r="0.8" fill="url(#accentGradient)" opacity="0.6" class="spark1"/>
          <circle cx="32" cy="32" r="0.8" fill="url(#accentGradient)" opacity="0.6" class="spark2"/>
        </g>

        <!-- 代码元素装饰：模拟代码行的装饰点 -->
        <g class="code-elements">
          <!-- 左侧代码点：模拟代码行号或缩进 -->
          <circle cx="12" cy="22" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>
          <circle cx="12" cy="30" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>
          <circle cx="12" cy="38" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>

          <!-- 右侧代码点：与左侧对称，增强编程感 -->
          <circle cx="48" cy="22" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>
          <circle cx="48" cy="30" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>
          <circle cx="48" cy="38" r="1.5" fill="url(#codeGradient)" opacity="0.7"/>
        </g>
      </g>

      <!-- 文字部分：包含主标题和副标题 -->
      <g class="logo-text">
        <!-- 主标题 "Code" -->
        <text
          :x="textConfig.codeX"
          :y="textConfig.mainY"
          font-family="'Inter', 'Helvetica Neue', Arial, sans-serif"
          :font-size="textConfig.mainSize"
          font-weight="700"
          fill="url(#primaryGradient)"
          class="main-text"
        >
          Code
        </text>

        <!-- 主标题 "Duel" -->
        <text
          :x="textConfig.duelX"
          :y="textConfig.mainY"
          font-family="'Inter', 'Helvetica Neue', Arial, sans-serif"
          :font-size="textConfig.mainSize"
          font-weight="700"
          fill="url(#accentGradient)"
          class="main-text"
        >
          Duel
        </text>

        <!-- 副标题：显示平台描述 -->
        <!-- 🎯 副标题大小调整位置：修改 textConfig.subtitleSize 的值 -->
        <text
          :x="textConfig.codeX"
          :y="textConfig.subtitleY"
          font-family="'Inter', 'Helvetica Neue', Arial, sans-serif"
          :font-size="textConfig.subtitleSize"
          font-weight="400"
          fill="#666"
          :opacity="showSubtitle ? 0.8 : 0.6"
          class="subtitle"
        >
          代码对战平台
        </text>
      </g>
    </svg>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue'

/**
 * CodeDuel Logo 组件
 * 功能：显示 CodeDuel 代码对战平台的 Logo
 * 特点：纯 SVG 绘制，支持自定义尺寸，响应式设计
 *
 * 🔧 快速调整参考：
 *
 * 📏 副标题大小调整：
 *   - 标准模式：textConfig.subtitleSize (第199行) - 默认 9px
 *   - 导航栏模式：textConfig.subtitleSize (第209行) - 默认 8px
 *   - 移动端：CSS .subtitle font-size (第258行) - 默认 8px
 *
 * 📐 文字位置调整：
 *   - Code 位置：textConfig.codeX (第191/201行) - 默认 70
 *   - Duel 位置：textConfig.duelX (第192/202行) - 默认 115
 *   - 主标题垂直：textConfig.mainY (第193/203行)
 *   - 副标题垂直：textConfig.subtitleY (第194/204行)
 *
 * 🎨 颜色调整：
 *   - 主色调：#409EFF → #337ecc (第14-16行)
 *   - 强调色：#667eea → #764ba2 (第19-21行)
 *   - 副标题色：fill="#666" (第131行)
 *
 * ⚡ 动画调整：
 *   - 过渡时间：transition: all 0.3s ease (第224行等)
 *   - 悬停效果：&:hover 部分 (第227-250行)
 */

// 组件属性定义
const props = defineProps({
  // Logo 宽度（像素）
  width: {
    type: Number,
    default: 180  // 默认宽度 180px
  },
  // Logo 高度（像素）
  height: {
    type: Number,
    default: 54   // 默认高度 54px
  },
  // 是否显示副标题（注意：导航栏版本会忽略此设置，始终显示副标题）
  showSubtitle: {
    type: Boolean,
    default: false
  }
})

/**
 * 动态计算 SVG 的 viewBox
 * viewBox 定义了 SVG 的可视区域和坐标系统
 * 格式：x y width height
 */
const computedViewBox = computed(() => {
  if (props.showSubtitle) {
    return "0 0 200 60"  // 标准模式：完整显示区域
  } else {
    return "0 0 200 60"  // 导航栏模式：保持完整高度确保副标题可见
  }
})

/**
 * 🎯 文字配置计算 - 主要的样式调整位置
 * 根据显示模式动态调整文字的位置、大小等属性
 */
const textConfig = computed(() => {
  if (props.showSubtitle) {
    // 标准模式：正常显示副标题
    return {
      codeX: 70,        // "Code" 文字的 X 坐标
      duelX: 115,       // "Duel" 文字的 X 坐标（调整此值可改变两个单词的间距）
      mainY: 25,        // 主标题的 Y 坐标（垂直位置）
      subtitleY: 40,    // 副标题的 Y 坐标（垂直位置）
      mainSize: 16,     // 主标题字体大小（px）
      subtitleSize: 9   // 🔧 副标题字体大小（px）- 修改此值调整副标题大小
    }
  } else {
    // 导航栏模式：紧凑布局，始终显示较小的副标题
    return {
      codeX: 70,        // "Code" 文字的 X 坐标
      duelX: 115,       // "Duel" 文字的 X 坐标（避免文字重合）
      mainY: 22,        // 主标题上移，为副标题留出空间
      subtitleY: 42,    // 副标题位置
      mainSize: 16,     // 主标题字体大小
      subtitleSize: 16   // 🔧 副标题字体大小（px）- 导航栏版本较小
    }
  }
})
</script>

<style lang="scss" scoped>
/**
 * CodeDuel Logo 样式
 * 包含基础样式、悬停效果、响应式设计
 */

.logo-container {
  display: inline-block;

  .codeduel-logo {
    // 基础过渡动画：所有变化都有平滑过渡效果
    transition: all 0.3s ease;

    // 🎨 悬停效果：鼠标悬停时的交互动画
    &:hover {
      // 代码括号悬停效果：加粗并添加发光
      .bracket-left,
      .bracket-right {
        stroke-width: 3;        // 线条加粗
        filter: url(#glow);     // 添加发光效果
      }

      // 对战剑悬停效果
      .battle-swords {
        .sword1,
        .sword2 {
          stroke-width: 2.5;     // 剑的线条加粗
        }

        // 交叉点发光增强
        .cross-glow {
          r: 4;                  // 发光半径增大
          opacity: 0.8;          // 透明度增加
        }
      }

      // 主标题文字发光效果
      .main-text {
        filter: url(#glow);
      }
    }
  }

  // 📱 响应式设计：移动端适配
  @media (max-width: 768px) {
    .codeduel-logo {
      // 移动端副标题字体调整
      .subtitle {
        font-size: 8px;         // 🔧 移动端副标题大小
      }

      // 移动端主标题字体调整
      .main-text {
        font-size: 14px;        // 🔧 移动端主标题大小
      }
    }
  }
}

// 🎬 动画过渡效果配置
.logo-icon {
  // 代码括号的过渡动画
  .bracket-left,
  .bracket-right {
    transition: all 0.3s ease;  // 平滑过渡：线条粗细、颜色、滤镜等
  }

  // 对战剑的过渡动画
  .battle-swords {
    .sword1,
    .sword2,
    .cross-glow {
      transition: all 0.3s ease;  // 平滑过渡：大小、透明度等
    }
  }
}

// 文字的过渡动画
.logo-text {
  .main-text,
  .subtitle {
    transition: all 0.3s ease;    // 平滑过渡：字体大小、颜色、滤镜等
  }
}
</style>
