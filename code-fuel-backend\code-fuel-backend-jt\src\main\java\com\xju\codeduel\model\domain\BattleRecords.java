package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 对战记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("battle_records")
@ApiModel(value="BattleRecords对象", description="对战记录")
public class BattleRecords implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

        @ApiModelProperty(value = "题目信息ID")
    @TableField("problem_id")
    private Long problemId;

        @ApiModelProperty(value = "是否是开房间（0-匹配 1-开房间）")
    @TableField("is_room")
    private Integer isRoom;

        @ApiModelProperty(value = "对战开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

        @ApiModelProperty(value = "对战结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

        @ApiModelProperty(value = "房间码")
    @TableField("room_code")
    private Long roomCode;


}
