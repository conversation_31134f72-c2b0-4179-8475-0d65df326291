package com.xju.codeduel.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评论信息DTO
 * 用于返回帖子下的评论列表信息
 */
@Data
@ApiModel(description = "评论信息DTO")
public class CommentDTO {

    @ApiModelProperty(value = "评论ID")
    private Integer commentId;

    @ApiModelProperty(value = "评论用户ID")
    private Integer userId;

    @ApiModelProperty(value = "评论用户名")
    private String username;

    @ApiModelProperty(value = "评论用户头像")
    private String userAvatar;

    @ApiModelProperty(value = "被回复的用户名（如果是回复评论）")
    private String parentUsername;

    @ApiModelProperty(value = "评论内容")
    private String content;

    @ApiModelProperty(value = "评论时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "父评论ID")
    private Integer parentId;
}