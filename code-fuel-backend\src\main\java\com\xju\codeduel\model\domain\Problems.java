package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 题目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("problems")
@ApiModel(value="Problems对象", description="题目信息")
public class Problems implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId("id")
    private Long id;

        @ApiModelProperty(value = "题目标题")
    @TableField("title")
    private String title;

        @ApiModelProperty(value = "题目难度值")
    @TableField("difficulty")
    private Integer difficulty;

        @ApiModelProperty(value = "比赛ID")
    @TableField("contest_id")
    private Integer contestId;

        @ApiModelProperty(value = "题目ID")
    @TableField("problem_id")
    private String problemId;

        @ApiModelProperty(value = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

        @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

        @ApiModelProperty(value = "逻辑删除 (0:正常, 1:删除)")
    @TableField("is_deleted")
        @TableLogic
    private Integer isDeleted;


}
