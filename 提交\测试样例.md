## [1.1 功能测试]()

### [1.1.1 还原炉管理]()

还原炉管理包含的主要界面为：还原炉概览和还原炉信息界面。还原炉概览涉及到的主要功能包括查询还原炉信息和查看详情。还原炉信息界面涉及到的功能有查询还原炉信息、修改还原炉信息、添加还原炉信息、删除还原炉信息。下面分别使用不同的测试用例来对不同的功能进行测试。

（1）使用管理员账号登录后在还原炉信息界面点击添加还原炉，按照提示以添加还原炉所需的必要输入信息项还原炉号、规格型号、所属组织为例，观察在必输入项填写完整与不完整时系统的响应是否符合预期的结果，添加还原炉功能测试结果如表 6-1 所示：

**表**​**6-1** **添加还原炉功能测试结果**

| 功能模块                             | 测试用例                   | 预期结果             | 实际结果             | 测试结果 |
| ------------------------------------ | -------------------------- | -------------------- | -------------------- | -------- |
| 添加还原炉信息功能                   | 输入项：还原炉号、规格型号 | 添加失败             | 提示所属组织不能为空 | 正确     |
| 输入项：规格型号、所属组织           | 添加失败                   | 提示还原炉号不能为空 | 正确                 |
| 输入项：还原炉号、所属组织           | 添加失败                   | 提示规格型号不能为空 | 正确                 |
| 输入项：还原炉号、规格型号、所属组织 | 添加成功                   | 添加成功             | 正确                 |

[（]()2）使用管理员账号登录后在还原炉信息界面点击查询还原炉，以查询条件运行状态为例，观察在系统的响应是否符合预期的结果，查询还原炉功能测试结果如表 6-2 所示：

**表**​**6-2 **​**查询还原炉功能测试结果**

| 功能模块           | 测试用例     | 预期结果                           | 实际结果                           | 测试结果 |
| ------------------ | ------------ | ---------------------------------- | ---------------------------------- | -------- |
| 查询还原炉信息功能 | 输入项：启炉 | 查询成功                           | 页面中显示出运行中的还原炉信息列表 | 正确     |
| 输入项：停炉       | 查询成功     | 页面中显示出已停炉的还原炉信息列表 | 正确                               |

### [1.1.2 基线管理]()

基线管理包含的主要界面为：料表基线和电压基线界面。料表基线界面的主要功能包括查询删除料表基线信息、上传下载料表基线文件和添加备注信息。电压基线界面的主要功能包括查询删除电压基线信息、上传下载电压基线文件和添加备注信息。下面分别使用不同的测试用例来对不同的功能进行测试。

（1）使用管理员账号登录后在料表基线界面点击上传料表基线文件，以上传料表文件为例，观察在上传空白料表文件与非空白料表文件时系统的响应是否符合预期的结果，测试上传料表文件功能测试结果如表 6-5 所示：

**表**​**6-5** **上传料表基线文件功能测试结果**

| 功能模块         | 测试用例         | 预期结果     | 实际结果         | 测试结果 |
| ---------------- | ---------------- | ------------ | ---------------- | -------- |
| 上传料表文件功能 | 输入项：空白文件 | 上传失败     | 提示文件解析失败 | 正确     |
| 输入项：料表文件 | 上传成功         | 提示上传成功 | 正确             |

（2）使用管理员账号登录后在电压基线界面点击查询电压基线，以查询条件测试电压基线为例，观察系统的响应是否符合预期的结果，查询电压基线功能测试结果如表 6-6 所示：

### [1.1.3 账户管理]()

账户管理包含的主要界面为：组织管理、角色管理和用户管理。组织管理界面的主要功能包括查询、删除、添加、修改组织信息。用户管理界面的主要功能包括查询、删除、添加、修改用户信息、启用账号、停用账号、重置账号密码。角色管理界面的主要功能包括查询、修改、添加、修改角色信息。下面分别使用不同的测试用例来对不同的功能进行测试。

（1）使用管理员账号登录后在组织管理界面点击添加组织信息，以添加测试组织为例，观察系统的响应是否符合预期的结果，测试添加组织信息测试结果如表 6-9 所示：

**表**​**6-9**​**添加组织信息功能测试结果**

| 功能模块         | 测试用例                           | 预期结果 | 实际结果     | 测试结果 |
| ---------------- | ---------------------------------- | -------- | ------------ | -------- |
| 添加组织功能测试 | 输入项：测试组织、测试、2023-10-31 | 添加成功 | 提示添加成功 | 正确     |

（2）使用管理员账号登录后在角色管理界面点击查询角色信息，以查询操作员和测试角色为例，观察系统的响应是否符合预期的结果，测试结果如表 6-10 所示：

**表**​**6-10** **查询角色信息功能测试结果**

| 功能模块         | 测试用例         | 预期结果           | 实际结果         | 测试结果 |
| ---------------- | ---------------- | ------------------ | ---------------- | -------- |
| 查询电压基线信息 | 输入项：测试角色 | 查询失败           | 页面显示暂无数据 | 正确     |
| 输入项：操作员   | 查询成功         | 页面显示操作员信息 | 正确             |

（3）使用管理员账号登录后在用户管理界面点击停用/启用账户，以停用启用测试用户为例，观察系统的响应是否符合预期的结果，、测试结果如表 6-11 所示：

**表**​**6-11** ​**停用**​**​/​**​**启用账户测试结果**

| 功能模块         | 测试用例 | 预期结果           | 实际结果           | 测试结果 |
| ---------------- | -------- | ------------------ | ------------------ | -------- |
| 删除电压基线功能 | 点击停用 | 停用成功           | 测试用户账号已停用 | 正确     |
| 点击启用         | 启用成功 | 测试用户账号已启用 | 正确               |

（4）使用管理员账号登录后在用户界面点击重置密码，以为重置测试用户密码为例，观察系统的响应是否符合预期的结果，测试结果如表 6-12 所示：

**表**​**6-12** **重置密码功能测试结果**

| 功能模块         | 测试用例         | 预期结果         | 实际结果                                         | 测试结果 |
| ---------------- | ---------------- | ---------------- | ------------------------------------------------ | -------- |
| 重置密码功能     | 输入项：11111111 | 重置失败         | 提示用户密码至少八位，包含至少一个数字和一个字母 | 正确     |
| 输入项：1111111a | 重置成功         | 提示重置密码成功 | 正确                                             |
