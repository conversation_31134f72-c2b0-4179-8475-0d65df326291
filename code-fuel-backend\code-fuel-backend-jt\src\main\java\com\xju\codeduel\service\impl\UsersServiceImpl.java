package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.common.utls.SessionUtils;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.domain.UserRatingHistories;
import com.xju.codeduel.mapper.UsersMapper;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.UserDTO;
import com.xju.codeduel.model.dto.UserRatingChangeDTO;
import com.xju.codeduel.service.IUsersService;
import com.xju.codeduel.service.IPostsService;
import com.xju.codeduel.service.IBattleRecordsService;
import com.xju.codeduel.service.IProblemsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements IUsersService {

    @Autowired
    private UsersMapper usersMapper;

    @Autowired
    private IPostsService postsService;

    @Autowired
    private IBattleRecordsService battleRecordsService;

    @Autowired
    private IProblemsService problemsService;

    @Override
    public Users login(Users users) {
        //条件构造器
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getCodeforcesId, users.getCodeforcesId());
        queryWrapper.eq(Users::getPassword, users.getPassword());
        Users users1 = usersMapper.selectOne(queryWrapper);

        // 如果登录成功，更新最后登录时间
        if (users1 != null) {
            users1.setLastLogin(java.time.LocalDateTime.now());
            // 更新数据库中的最后登录时间
            this.updateById(users1);
        }

        SessionUtils.saveCurrentUserInfo(users1);

        return users1;
    }

    @Override
    public Page<Users> pageList(PageDTO pageDTO, UserDTO users) {
        Page<Users> page = new Page<Users>(pageDTO.getPageNo(), pageDTO.getPageSize());
        page = usersMapper.pagelist(page, users);
        return page;
    }

    @Override
    public Page<UserRatingChangeDTO> getUsersWithRatingHistory(PageDTO pageDTO, String username) {
        // 使用传入的分页参数，而不是硬编码
        Page<UserRatingChangeDTO> page = new Page<UserRatingChangeDTO>(
            pageDTO.getPageNo(),
            pageDTO.getPageSize()
        );
        page = usersMapper.getUsersWithRatingHistory(page, username);
        return page;
    }

    @Override
    public Page<UserDTO> getUsersListWithBattleCount(PageDTO pageDTO, String username) {
        Page<UserDTO> page = new Page<UserDTO>(
            pageDTO.getPageNo(),
            pageDTO.getPageSize()
        );
        page = usersMapper.getUsersListWithBattleCount(page, username);
        return page;
    }

    @Override
    public UserRatingChangeDTO getUserRatingHistoryByUsername(String username) {
        List<UserRatingChangeDTO> results = usersMapper.getUserRatingHistoryByUsername(username);

        if (results == null || results.isEmpty()) {
            return null;
        }

        // 合并结果：取第一个作为基础，合并所有的历史记录
        UserRatingChangeDTO mergedResult = new UserRatingChangeDTO();
        mergedResult.setUser(results.get(0).getUser());

        // 收集所有的历史记录
        List<UserRatingHistories> allHistories = new ArrayList<>();
        for (UserRatingChangeDTO result : results) {
            if (result.getRatingHistories() != null && !result.getRatingHistories().isEmpty()) {
                allHistories.addAll(result.getRatingHistories());
            }
        }

        mergedResult.setRatingHistories(allHistories);
        return mergedResult;
    }



    @Override
    public Users getUserByUsername(String username) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getCodeforcesId, username);
        return usersMapper.selectOne(queryWrapper);
    }

    @Override
    public Map<String, Object> getHomeStats() {
        Map<String, Object> stats = new HashMap<>();

        // 获取总用户数
        long totalUsers = this.count();
        stats.put("totalUsers", totalUsers);

        // 获取总对战数
        long totalBattles = battleRecordsService.count();
        stats.put("totalBattles", totalBattles);

        // 获取题目总数
        long totalProblems = problemsService.count();
        stats.put("totalProblems", totalProblems);

        // 获取发帖总数
        long totalPosts = postsService.count();
        stats.put("totalPosts", totalPosts);

        return stats;
    }
}
