package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Comments;
import com.xju.codeduel.mapper.CommentsMapper;
import com.xju.codeduel.model.dto.CommentDTO;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.service.ICommentsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 评论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class CommentsServiceImpl extends ServiceImpl<CommentsMapper, Comments> implements ICommentsService {
    @Autowired
    private CommentsMapper commentsMapper;

    @Override
    public Page<CommentDTO> getCommentsByPostId(PageDTO pageDTO, Integer postId) {
        if (postId == null) {
            throw new IllegalArgumentException("帖子ID不能为空");
        }
        if (pageDTO == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        // 创建分页对象
        Page<CommentDTO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        // 执行分页查询
        return commentsMapper.getCommentsByPostId(page, postId);
    }
}
