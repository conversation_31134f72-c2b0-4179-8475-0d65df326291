package com.xju.codeduel.web.controller;


import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.SessionUtils;
import com.xju.codeduel.model.domain.Users;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @GetMapping("getUserInfo")
    public JsonResponse getUserInfo() {
        Users user = SessionUtils.getCurrentUserInfo();

        System.out.println(user);
        return JsonResponse.success(user);
    }


}
