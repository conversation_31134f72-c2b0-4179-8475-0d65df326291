我的题目是：

| ​**论文题目**​**:** | **基于**​**Spring Boot+Vue**​**的**​**Codeforces**​**对战系统（讨论区模块、聊天区模块、题目管理模块）** |
| ------------------- | ------------------------------------------------------------------------------------------------------- |

需要严格遵守上面板块来写我的个人模块

[摘要论文的高度概括]()[[m1]](#\_msocom_1) ，是全文的缩影，是长篇论文不可缺少的组成部分。要求用中、英文分别书写，一篇摘要不少于 400 字符。摘要一般写成 3 段 ：

第 1 段写实习实训论文题目的背景、意义，就是说，为什么要选这个题目，设计开发的意义是什么（约 100-150 字，绝对不允许超过 150 字）。

第 2 段写实习实训论文采用了何种技术，做了哪些内容，开发了哪些模块，完成了哪些功能，具有什么特点等（约 180-230 字）。

第 3 段写实习实训系统实现的效果，如，实际测试和运行的情况，系统的性能如何，带来了哪些优势，应用价值如何，应用前景如何等（约 70-170 字）。

[**关**]() **键** **词**[[m2]](#\_msocom_2) **​：​**XXX；XXX；XXX；XXX；XXX

---

摘要正文：

1、每段开头空两字符，为小四号宋体，内容为 1.5 倍行距。

2、出现的英文字体为：Times New Roman。

1、在摘要正文结束，空一行后，左对齐，写“关键词”三字，“关键词”是小四、宋体、加粗，三个字中间分别空一个字符。

2、“关键词”三个字后面是小四、宋体、加粗的冒号，没有空格。

3、冒号其后为小四、宋体的关键词（不加粗），关键词之间用中文分号“；”分隔，中间没有空格，最后一个关键词后无标点符号。

4、关键词的个数 3－5 个，选取关键的领域名词和技术名词。

5、若摘要内容超出一页，都单面编排打印。

# [1 总结与展望]()

结论与展望：结论包括对整个研究工作进行归纳和综合而得出的总结；所得结果与已有结果的比较；联系实际结果，指出它的学术意义或应用价值和在实际中推广应用的可能性；在本课题研究中尚存在的问题，对进一步开展研究的见解与建议。结论集中反映作者的研究成果，表达作者对所研究课题的见解和主张，是全文的思想精髓，是全文的思想体现，一般应写得概括、篇幅较短。撰写时应注意下列事项：

● 结论要简单、明确。在措辞上应严密，但又容易被人领会。

● 结论应反映个人的研究工作，属于前人和他人已有过的结论可少提。

● 要实事求是地介绍自己研究的结果，切忌言过其实，在无充分把握时，应留有余地。

**对该论文的总结（摘要的详细版），对未来工作的展望，不要写成实训的心得体会。**



在（论文.md）中编写根据这个 codeduel 的项目论文，学习（以前的论文.md）他的格式，模板，结合我当前项目来进行书写，如果有图片的需要请留空并且在空位说出图片的要求，怎么样的,要求 1.2w 字

