package com.xju.codeduel.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * CORS（跨源资源共享）配置类
 *
 * 问题背景：
 * 浏览器的同源策略限制了前端JavaScript访问不同源的资源。
 * 在开发环境中，前端（localhost:4040）需要访问后端API（localhost:8081），
 * 这属于跨域请求，需要配置CORS来允许这种访问。
 *
 * 解决方案：
 * 1. 配置允许的源、方法、头部
 * 2. 处理预检请求（OPTIONS）
 * 3. 设置凭证支持和缓存时间
 *
 * 技术实现：
 * - 实现WebMvcConfigurer接口，重写addCorsMappings方法
 * - 提供CorsConfigurationSource Bean，支持更细粒度的配置
 *
 * 安全考虑：
 * 生产环境应该限制allowedOriginPatterns为具体的域名
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.addAllowedOriginPattern("*");
        configuration.addAllowedMethod("*");
        configuration.addAllowedHeader("*");
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
