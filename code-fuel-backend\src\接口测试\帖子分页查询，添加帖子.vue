<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox, ElTable, ElTableColumn, ElPagination, ElDialog, ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'
import avatar from '@/assets/default.png'
import { userInfoService } from "@/api/user"
import { useUserInfoStore } from "@/stores/userInfo"
import { useRouter } from "vue-router"
import { getInfo } from "@/api/api"

const userInfoStore = useUserInfoStore()
const router = useRouter()

// 使用 async/await 重写获取用户信息的函数
const getUserInfo = async () => {
  try {
    const res = await getInfo()
    console.log('getInfo 返回结果:', res) // 打印返回结果，方便调试
    if (res.data == null) {
      ElMessage.error("暂未登录，请先登录")
      router.push('/login')
    } else {
      userInfoStore.setUserInfo(res.data)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    if (error.response) {
      // 服务器返回了错误响应
      ElMessage.error(`获取用户信息失败，状态码: ${error.response.status}，错误信息: ${error.response.data.message || '未知错误'}`)
    } else if (error.request) {
      // 请求已发送，但没有收到响应
      ElMessage.error('获取用户信息失败，没有收到服务器响应，请检查网络连接')
    } else {
      // 其他错误
      ElMessage.error(`获取用户信息失败: ${error.message}`)
    }
    router.push('/login')
  }
}

// 调用函数获取用户信息
getUserInfo()

// 原始测试方法保持不变
async function searchPostsByTitle(title) {
  try {
    const response = await fetch(`/api/posts/searchByTitle?title=${encodeURIComponent(title)}`)
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }
    const data = await response.json()
    return data.data
  } catch (error) {
    console.error('Error fetching posts:', error)
    ElMessage.error('获取数据失败: ' + error.message)
    return []
  }
}

const testTitle = ref('算法')
const testResult = ref(null)

async function runTest() {
  testResult.value = '加载中...'
  const result = await searchPostsByTitle(testTitle.value)
  testResult.value = result
  console.log('测试结果:', result)
  if (result && result.length > 0) {
    ElMessage.success(`成功获取到 ${result.length} 条数据`)
  } else {
    ElMessage.warning('没有获取到数据')
  }
}

// 表格测试功能
const tableTitle = ref('算法')
const tableData = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)

async function fetchPostsWithPagination() {
  loading.value = true
  try {
    const response = await fetch(
      `/api/posts/pagePosts?title=${encodeURIComponent(tableTitle.value)}&pageNo=${currentPage.value}&pageSize=${pageSize.value}`
    )
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }
    const data = await response.json()
    tableData.value = data.data.records || []
    total.value = data.data.total || 0
  } catch (error) {
    console.error('Error fetching posts:', error)
    ElMessage.error('获取数据失败: ' + error.message)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

function handleCurrentChange(val) {
  currentPage.value = val
  fetchPostsWithPagination()
}

function handleSizeChange(val) {
  pageSize.value = val
  fetchPostsWithPagination()
}

// 写帖子功能
const dialogVisible = ref(false)
const postForm = ref({
  title: '',
  content: ''
})
const formRef = ref(null)
const formRules = {
  title: [
    { required: true, message: '请输入帖子标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入帖子内容', trigger: 'blur' },
    { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }
  ]
}

// 打开写帖子对话框
function openPostDialog() {
  // 检查用户是否登录
  console.log("----------------------\n")

  // 检查 userInfoStore 是否正常
  if (!userInfoStore) {
    console.error("userInfoStore is not initialized!")
    ElMessage.error("用户存储未初始化，请刷新页面")
    return
  }
  console.log(userInfoStore.info)
  // 直接打印，避免 JSON 序列化问题
  console.log('当前用户信息:', userInfoStore.userInfo)
  if (!userInfoStore.userInfo?.id) {
    ElMessageBox.confirm('您需要登录后才能发帖，是否前往登录页面？', '提示', {
      confirmButtonText: '去登录',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      router.push('/login')
    }).catch(() => {
      ElMessage.info('已取消登录')
    })
    return
  }

  // 重置表单
  postForm.value = {
    title: '',
    content: ''
  }
  dialogVisible.value = true
}

// 提交帖子
async function submitPost() {
  try {
    // 验证表单
    await formRef.value.validate()

    // 构造请求数据
    const postData = {
      title: postForm.value.title,
      content: postForm.value.content,
      userId: userInfoStore.userInfo.id, // 从store获取用户ID
      avatar: avatar // 使用默认头像
    }

    // 发送请求
    const response = await fetch('/api/posts/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData)
    })

    if (!response.ok) {
      throw new Error('提交失败')
    }

    const result = await response.json()
    console.log('后端返回的完整结果:', result); // 打印完整响应数据

    // 检查后端返回的 status 字段，判断是否为 true
    if (result.status === true) {
      ElMessage.success('发帖成功！')
      dialogVisible.value = false
      // 刷新表格数据
      fetchPostsWithPagination()
    } else {
      throw new Error(result.message || '发帖失败')
    }
  } catch (error) {
    console.error('提交错误:', error)
    // 打印后端返回的完整结果，方便调试
    if (error.response) {
      console.error('后端返回的响应:', error.response.data)
    }
    ElMessage.error(error.message || '发帖失败，请检查表单')
  }
}

// 初始加载
fetchPostsWithPagination()

</script>

<template>
  <div class="test-container">
    <!-- 原始测试方法 -->
    <h3>原始测试方法</h3>
    <el-input v-model="testTitle" placeholder="输入搜索标题" style="width: 200px; margin-right: 10px" />
    <el-button type="primary" @click="runTest">测试搜索</el-button>

    <div v-if="testResult !== null" style="margin-top: 20px">
      <h4>测试结果:</h4>
      <pre>{{ testResult }}</pre>
    </div>

    <el-divider />

    <!-- 表格测试 -->
    <div class="header-box">
      <h3>帖子列表</h3>
      <el-button type="primary" @click="openPostDialog">写帖子</el-button>
    </div>

    <div class="search-box">
      <el-input v-model="tableTitle" placeholder="输入搜索标题" style="width: 200px; margin-right: 10px" />
      <el-button type="primary" @click="fetchPostsWithPagination">搜索</el-button>
    </div>

    <el-table
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      v-loading="loading"
      border
    >
      <el-table-column prop="post.id" label="帖子ID" width="100" />
      <el-table-column prop="post.title" label="帖子标题" width="180" />
      <el-table-column prop="post.isTop" label="是否置顶" width="100">
        <template #default="{ row }">
          <el-tag :type="row.post.isTop ? 'success' : 'info'">
            {{ row.post.isTop ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="user.id" label="用户ID" width="100" />
      <el-table-column prop="user.codeforcesId" label="用户Codeforces ID" width="150" />
      <el-table-column prop="user.rating" label="用户Rating" width="120" />
      <el-table-column prop="post.postTime" label="发布时间" width="180" />
    </el-table>

    <div class="pagination-box">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 写帖子对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="发表新帖子"
      width="50%"
      :before-close="() => dialogVisible = false"
    >
      <el-form
        ref="formRef"
        :model="postForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="postForm.title" placeholder="请输入帖子标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="postForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入帖子内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPost">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.test-container {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 20px;
}

.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-box h3 {
  margin: 0;
  color: #409eff;
}

.search-box {
  margin-bottom: 20px;
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

h3 {
  margin-bottom: 15px;
  color: #409eff;
}
</style>
<!--接口返回的数据格式为-->
<!--{-->
<!--"status": true,-->
<!--"code": 0,-->
<!--"message": null,-->
<!--"data": [-->
<!--{-->
<!--"post": {-->
<!--"id": 22,-->
<!--"userId": 1,-->
<!--"title": "ansldskfe",-->
<!--"content": "阿三顶顶发达的的v是多少",-->
<!--"isTop": 0,-->
<!--"postTime": "2025-07-19T12:16:52",-->
<!--"updateTime": "2025-07-19T14:30:49",-->
<!--"isDeleted": 0,-->
<!--"likeCount": 0-->
<!--},-->
<!--"user": {-->
<!--"id": 1,-->
<!--"password": "123456",-->
<!--"codeforcesId": "admin",-->
<!--"avatar": "https://userpic.codeforces.org/no-title.jpg",-->
<!--"rating": 0,-->
<!--"status": 0,-->
<!--"banReason": null,-->
<!--"registerTime": "2025-07-02T23:24:08",-->
<!--"updateTime": "2025-07-16T16:47:47",-->
<!--"isAdmin": 2,-->
<!--"lastLogin": "2025-07-12T23:24:26"-->
<!--}-->
<!--},-->
<!--{-->
<!--"post": {-->
<!--"id": 26,-->
<!--"userId": 1,-->
<!--"title": "anssndkfnas",-->
<!--"content": "rsfhnzsdzbhdfbsxdcbvbsd",-->
<!--"isTop": 0,-->
<!--"postTime": "2025-07-19T12:29:32",-->
<!--"updateTime": "2025-07-19T14:30:43",-->
<!--"isDeleted": 0,-->
<!--"likeCount": 0-->
<!--},-->
<!--"user": {-->
<!--"id": 1,-->
<!--"password": "123456",-->
<!--"codeforcesId": "admin",-->
<!--"avatar": "https://userpic.codeforces.org/no-title.jpg",-->
<!--"rating": 0,-->
<!--"status": 0,-->
<!--"banReason": null,-->
<!--"registerTime": "2025-07-02T23:24:08",-->
<!--"updateTime": "2025-07-16T16:47:47",-->
<!--"isAdmin": 2,-->
<!--"lastLogin": "2025-07-12T23:24:26"-->
<!--}-->
<!--}-->
<!--],-->
<!--"otherData": {}-->
<!--}-->


<!--分页查询返回格式-->
<!--{-->
<!--"status": true,-->
<!--"code": 0,-->
<!--"message": null,-->
<!--"data": {-->
<!--"records": [-->
<!--{-->
<!--"post": {-->
<!--"id": 26,-->
<!--"userId": 1,-->
<!--"title": "anssndkfnas",-->
<!--"content": "rsfhnzsdzbhdfbsxdcbvbsd",-->
<!--"isTop": 0,-->
<!--"postTime": "2025-07-19T12:29:32",-->
<!--"updateTime": "2025-07-19T14:30:43",-->
<!--"isDeleted": 0,-->
<!--"likeCount": 0-->
<!--},-->
<!--"user": {-->
<!--"id": 1,-->
<!--"password": "123456",-->
<!--"codeforcesId": "admin",-->
<!--"avatar": "https://userpic.codeforces.org/no-title.jpg",-->
<!--"rating": 0,-->
<!--"status": 0,-->
<!--"banReason": null,-->
<!--"registerTime": "2025-07-02T23:24:08",-->
<!--"updateTime": "2025-07-16T16:47:47",-->
<!--"isAdmin": 2,-->
<!--"lastLogin": "2025-07-12T23:24:26"-->
<!--}-->
<!--},-->
<!--{-->
<!--"post": {-->
<!--"id": 22,-->
<!--"userId": 1,-->
<!--"title": "ansldskfe",-->
<!--"content": "阿三顶顶发达的的v是多少",-->
<!--"isTop": 0,-->
<!--"postTime": "2025-07-19T12:16:52",-->
<!--"updateTime": "2025-07-19T14:30:49",-->
<!--"isDeleted": 0,-->
<!--"likeCount": 0-->
<!--},-->
<!--"user": {-->
<!--"id": 1,-->
<!--"password": "123456",-->
<!--"codeforcesId": "admin",-->
<!--"avatar": "https://userpic.codeforces.org/no-title.jpg",-->
<!--"rating": 0,-->
<!--"status": 0,-->
<!--"banReason": null,-->
<!--"registerTime": "2025-07-02T23:24:08",-->
<!--"updateTime": "2025-07-16T16:47:47",-->
<!--"isAdmin": 2,-->
<!--"lastLogin": "2025-07-12T23:24:26"-->
<!--}-->
<!--}-->
<!--],-->
<!--"total": 2,-->
<!--"size": 5,-->
<!--"current": 1,-->
<!--"pages": 1-->
<!--},-->
<!--"otherData": {}-->
<!--}-->