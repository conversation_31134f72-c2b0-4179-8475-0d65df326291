<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.ProblemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.Problems">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="difficulty" property="difficulty" />
        <result column="contest_id" property="contestId" />
        <result column="problem_id" property="problemId" />
        <result column="created_time" property="createdTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

</mapper>
