<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.UsersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.Users">
        <id column="id" property="id" />
        <result column="password" property="password" />
        <result column="codeforces_id" property="codeforcesId" />
        <result column="avatar" property="avatar" />
        <result column="rating" property="rating" />
        <result column="status" property="status" />
        <result column="ban_reason" property="banReason" />
        <result column="register_time" property="registerTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_admin" property="isAdmin" />
        <result column="last_login" property="lastLogin" />
    </resultMap>

    <!-- 1. 定义结果映射 -->
    <resultMap id="userResultMap" type="com.xju.codeduel.model.domain.Users">
        <id property="id" column="id"/>
        <result property="password" column="password"/>
        <result property="codeforcesId" column="codeforces_id"/>
        <result property="avatar" column="avatar"/>
        <result property="rating" column="rating"/>
        <result property="status" column="status"/>
        <result property="banReason" column="ban_reason"/>
        <result property="registerTime" column="register_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isAdmin" column="is_admin"/>
        <result property="lastLogin" column="last_login"/>
    </resultMap>

    <resultMap id="ratingHistoryResultMap" type="com.xju.codeduel.model.domain.UserRatingHistories">
        <id property="id" column="rh_id"/>
        <result property="userId" column="user_id"/>
        <result property="battleId" column="battle_id"/>
        <result property="oldRating" column="old_rating"/>
        <result property="newRating" column="new_rating"/>
        <result property="reason" column="reason"/>
        <result property="recordTime" column="record_time"/>
    </resultMap>

    <!-- 复合结果映射 -->
    <resultMap id="userRatingChangeResultMap" type="com.xju.codeduel.model.dto.UserRatingChangeDTO">
        <association property="user" resultMap="userResultMap"/>
        <collection property="ratingHistories" resultMap="ratingHistoryResultMap"
                    ofType="com.xju.codeduel.model.domain.UserRatingHistories"/>
    </resultMap>

    <select id="pagelist" resultType="com.xju.codeduel.model.domain.Users">
        select * from users
        <where>
            <if test="user.codeforcesId != null and user.codeforcesId != ''">
                and codeforces_id like concat('%', #{user.codeforcesId}, '%')
            </if>
            <if test="user.isAdmin != null">
                and is_admin = #{user.isAdmin}
            </if>
        </where>
    </select>
    <!-- 2. 定义SQL片段 -->
    <sql id="userColumns">
        u.id, u.password, u.codeforces_id, u.avatar, u.rating,
        u.status, u.ban_reason, u.register_time, u.update_time,
        u.is_admin, u.last_login
    </sql>

    <sql id="ratingHistoryColumns">
        rh.id as rh_id, rh.user_id, rh.battle_id, rh.old_rating,
        rh.new_rating, rh.reason, rh.record_time
    </sql>

    <!-- 3. 查询方法 -->
    <select id="getUsersWithRatingHistory" resultMap="userRatingChangeResultMap">
        SELECT
        <include refid="userColumns"/>,
        <include refid="ratingHistoryColumns"/>
        FROM
        users u
        LEFT JOIN
        user_rating_histories rh ON u.id = rh.user_id
        <where>
            <if test="username != null and username != ''">
                AND u.codeforces_id LIKE CONCAT('%', #{username}, '%')
            </if>
        </where>
        ORDER BY
        u.rating DESC, u.id, rh.record_time DESC
    </select>

    <!-- 4. 获取用户列表（仅基本信息 + 对战记录条数） -->
    <select id="getUsersListWithBattleCount" resultType="com.xju.codeduel.model.dto.UserDTO">
        SELECT
            u.id, u.password, u.codeforces_id, u.avatar, u.rating,
            u.status, u.ban_reason, u.register_time, u.update_time,
            u.is_admin, u.last_login,
            COALESCE(battle_count.total_battles, 0) as totalBattles
        FROM users u
        LEFT JOIN (
            SELECT user_id, COUNT(*) as total_battles
            FROM user_rating_histories
            GROUP BY user_id
        ) battle_count ON u.id = battle_count.user_id
        <where>
            <if test="username != null and username != ''">
                AND u.codeforces_id LIKE CONCAT('%', #{username}, '%')
            </if>
        </where>
        ORDER BY u.rating DESC, u.id
    </select>

    <!-- 5. 根据用户名精确查询用户及其完整Rating历史记录 -->
    <select id="getUserRatingHistoryByUsername" resultMap="userRatingChangeResultMap">
        SELECT
        <include refid="userColumns"/>,
        <include refid="ratingHistoryColumns"/>
        FROM
        users u
        LEFT JOIN
        user_rating_histories rh ON u.id = rh.user_id
        WHERE
        u.codeforces_id = #{username}
        ORDER BY
        rh.record_time DESC
    </select>


</mapper>
