package com.xju.codeduel.model.dto;

import com.xju.codeduel.model.domain.Problems;
import com.xju.codeduel.model.domain.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 房间信息DTO
 * 用于返回房间的详细信息给前端
 * 
 * 功能说明：
 * 1. 封装房间的完整信息，包括基本信息、参与者、题目等
 * 2. 提供房间状态管理和显示
 * 3. 支持实时更新房间信息
 * 
 * 房间状态说明：
 * - WAITING: 等待中（刚创建，等待其他用户加入）
 * - READY: 准备就绪（有足够参与者，可以开始对战）
 * - BATTLING: 对战中（正在进行对战）
 * - FINISHED: 已结束（对战完成）
 * 
 * 使用场景：
 * - 返回房间详细信息给前端显示
 * - WebSocket推送房间状态更新
 * - 房间列表展示
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@ApiModel(value = "RoomInfoDTO", description = "房间信息数据")
public class RoomInfoDTO {

    @ApiModelProperty(value = "房间ID", example = "1")
    private Long roomId;

    @ApiModelProperty(value = "房间码", example = "123456")
    private Long roomCode;

    @ApiModelProperty(value = "房间状态", example = "WAITING", 
                     notes = "WAITING-等待中, READY-准备就绪, BATTLING-对战中, FINISHED-已结束")
    private String status;

    @ApiModelProperty(value = "创建者信息")
    private Users creator;

    @ApiModelProperty(value = "参与者列表")
    private List<Users> participants;

    @ApiModelProperty(value = "题目信息")
    private Problems problem;

    @ApiModelProperty(value = "房间描述", example = "欢迎来到我的房间")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "对战开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "对战结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "当前参与者数量", example = "2")
    private Integer participantCount;

    @ApiModelProperty(value = "最大参与者数量", example = "2", notes = "目前固定为2人对战")
    private Integer maxParticipants;

    @ApiModelProperty(value = "对战结果信息")
    private BattleResult battleResult;

    @ApiModelProperty(value = "是否为匹配对战", example = "false",
                     notes = "true-匹配对战(计算分数), false-房间对战(不计算分数)")
    private Boolean isMatchBattle;

    /**
     * 房间状态枚举
     */
    public enum RoomStatus {
        WAITING("等待中"),
        READY("准备就绪"),
        BATTLING("对战中"),
        FINISHED("已结束");

        private final String description;

        RoomStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 对战结果信息
     */
    public static class BattleResult {
        @ApiModelProperty(value = "获胜者ID")
        private Long winnerId;

        @ApiModelProperty(value = "结束原因", example = "SOLVED",
                         notes = "SOLVED-解题获胜, SURRENDER-对手投降, QUIT-对手退出")
        private String reason;

        @ApiModelProperty(value = "对战结束时间")
        private LocalDateTime endTime;

        public Long getWinnerId() {
            return winnerId;
        }

        public void setWinnerId(Long winnerId) {
            this.winnerId = winnerId;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }
    }

    /**
     * 默认构造函数
     */
    public RoomInfoDTO() {
        this.maxParticipants = 2; // 默认2人对战
    }

    // ==================== Getter和Setter方法 ====================

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public Long getRoomCode() {
        return roomCode;
    }

    public void setRoomCode(Long roomCode) {
        this.roomCode = roomCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Users getCreator() {
        return creator;
    }

    public void setCreator(Users creator) {
        this.creator = creator;
    }

    public List<Users> getParticipants() {
        return participants;
    }

    public void setParticipants(List<Users> participants) {
        this.participants = participants;
        // 自动更新参与者数量
        this.participantCount = participants != null ? participants.size() : 0;
    }

    public Problems getProblem() {
        return problem;
    }

    public void setProblem(Problems problem) {
        this.problem = problem;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getParticipantCount() {
        return participantCount;
    }

    public void setParticipantCount(Integer participantCount) {
        this.participantCount = participantCount;
    }

    public Integer getMaxParticipants() {
        return maxParticipants;
    }

    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }

    public BattleResult getBattleResult() {
        return battleResult;
    }

    public void setBattleResult(BattleResult battleResult) {
        this.battleResult = battleResult;
    }

    public Boolean getIsMatchBattle() {
        return isMatchBattle;
    }

    public void setIsMatchBattle(Boolean isMatchBattle) {
        this.isMatchBattle = isMatchBattle;
    }

    /**
     * 判断房间是否已满
     * 
     * @return true-房间已满，false-房间未满
     */
    public boolean isFull() {
        return participantCount != null && maxParticipants != null && 
               participantCount >= maxParticipants;
    }

    /**
     * 判断房间是否可以开始对战
     * 
     * @return true-可以开始，false-不能开始
     */
    public boolean canStart() {
        return participantCount != null && participantCount >= 2;
    }

    /**
     * 重写toString方法，便于日志记录和调试
     */
    @Override
    public String toString() {
        return "RoomInfoDTO{" +
                "roomId=" + roomId +
                ", roomCode=" + roomCode +
                ", status='" + status + '\'' +
                ", participantCount=" + participantCount +
                ", maxParticipants=" + maxParticipants +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
