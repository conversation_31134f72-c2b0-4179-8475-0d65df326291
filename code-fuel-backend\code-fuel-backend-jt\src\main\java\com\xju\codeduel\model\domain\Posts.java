package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 帖子
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("posts")
@ApiModel(value="Posts对象", description="帖子")
public class Posts implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

        @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

        @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;

        @ApiModelProperty(value = "内容")
    @TableField("content")
    private String content;

        @ApiModelProperty(value = "置顶标志（0正常1置顶）")
    @TableField("is_top")
    private Integer isTop;

        @ApiModelProperty(value = "发帖时间")
    @TableField("post_time")
    private LocalDateTime postTime;

        @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

        @ApiModelProperty(value = "逻辑删除 (0:正常, 1:删除)")
    @TableField("is_deleted")
        @TableLogic
    private Integer isDeleted;

        @ApiModelProperty(value = "评论数")
    @TableField("comment_count")
    private Integer commentCount;


}
