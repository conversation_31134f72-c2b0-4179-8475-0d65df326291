package com.xju.codeduel.model.dto;

import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.domain.UserRatingHistories;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "UserRatingHistoryDTO对象", description = "用户Rating历史记录DTO")
public class UserRatingChangeDTO {
    @ApiModelProperty(value = "用户信息")
    private Users user;

    @ApiModelProperty(value = "Rating变化历史记录(按时间倒序)")
    private List<UserRatingHistories> ratingHistories;
}