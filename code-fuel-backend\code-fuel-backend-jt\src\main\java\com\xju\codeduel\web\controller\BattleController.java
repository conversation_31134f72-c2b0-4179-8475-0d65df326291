package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.PythonServiceUtils;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.service.IRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 对战控制器
 * 
 * 功能说明：
 * 1. 处理对战过程中的各种操作
 * 2. 检查用户提交状态
 * 3. 处理对战结束逻辑
 * 4. 与Python服务交互验证提交
 * 
 * API接口：
 * - POST /api/battle/check-submission - 检查提交状态
 * - POST /api/battle/finish - 结束对战
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/api/battle")
@Api(tags = "对战管理", description = "对战过程中的各种操作")
public class BattleController {

    private static final Logger logger = LoggerFactory.getLogger(BattleController.class);

    @Autowired
    private IRoomService roomService;

    @Autowired
    private PythonServiceUtils pythonServiceUtils;

    /**
     * 检查用户提交状态
     * 
     * 接口说明：
     * 1. 接收用户的Codeforces用户名和题目ID
     * 2. 调用Python服务检查用户在Codeforces上的提交状态
     * 3. 返回是否通过（Accepted）
     * 
     * @param requestData 请求数据，包含codeforcesHandle和problemId
     * @return 提交状态检查结果
     */
    @PostMapping("/check-submission")
    @ApiOperation(value = "检查提交状态", notes = "检查用户在Codeforces上的提交状态")
    public JsonResponse<Map<String, Object>> checkSubmissionStatus(
            @ApiParam(value = "检查提交状态请求数据", required = true)
            @RequestBody Map<String, String> requestData) {
        
        String codeforcesHandle = requestData.get("codeforcesHandle");
        String problemId = requestData.get("problemId");
        
        logger.info("🔍 检查提交状态，用户: {}, 题目: {}", codeforcesHandle, problemId);
        
        try {
            // 参数验证
            if (codeforcesHandle == null || codeforcesHandle.trim().isEmpty()) {
                return JsonResponse.failure("Codeforces用户名不能为空");
            }
            
            if (problemId == null || problemId.trim().isEmpty()) {
                return JsonResponse.failure("题目ID不能为空");
            }
            
            // 检查Python服务是否可用
            if (!pythonServiceUtils.isServiceAvailable()) {
                return JsonResponse.failure("Python服务暂时不可用，请稍后重试");
            }
            
            // 调用Python服务检查提交状态
            boolean isPassed = pythonServiceUtils.checkSubmissionStatus(codeforcesHandle, problemId);
            
            // 构建响应数据
            Map<String, Object> responseData = Map.of(
                "isPassed", isPassed,
                "codeforcesHandle", codeforcesHandle,
                "problemId", problemId,
                "checkTime", System.currentTimeMillis()
            );
            
            logger.info("✅ 提交状态检查完成，用户: {}, 题目: {}, 结果: {}", 
                       codeforcesHandle, problemId, isPassed ? "通过" : "未通过");
            
            return JsonResponse.success(responseData);
            
        } catch (Exception e) {
            logger.error("❌ 检查提交状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("检查提交状态失败: " + e.getMessage());
        }
    }

    /**
     * 结束对战
     *
     * 接口说明：
     * 1. 接收房间码、获胜者ID、对战时间、结束原因等信息
     * 2. 更新房间状态为已结束
     * 3. 保存对战结果到数据库
     * 4. 计算并更新用户Rating
     *
     * @param requestData 请求数据，包含roomCode、winnerId、battleTime、reason等
     * @return 对战结束处理结果
     */
    @PostMapping("/finish")
    @ApiOperation(value = "结束对战", notes = "结束对战并保存结果")
    public JsonResponse<RoomInfoDTO> finishBattle(
            @ApiParam(value = "结束对战请求数据", required = true)
            @RequestBody Map<String, Object> requestData) {

        Long roomCode = Long.valueOf(requestData.get("roomCode").toString());
        Long winnerId = requestData.get("winnerId") != null ?
                       Long.valueOf(requestData.get("winnerId").toString()) : null;
        Integer battleTime = requestData.get("battleTime") != null ?
                            Integer.valueOf(requestData.get("battleTime").toString()) : 0;
        String reason = requestData.get("reason") != null ?
                       requestData.get("reason").toString() : "UNKNOWN";

        logger.info("🏁 结束对战，房间: {}, 获胜者: {}, 对战时间: {}秒, 原因: {}",
                   roomCode, winnerId, battleTime, reason);
        
        try {
            // 参数验证
            if (roomCode == null) {
                return JsonResponse.failure("房间码不能为空");
            }
            
            // 调用房间服务结束对战
            RoomInfoDTO roomInfo = roomService.finishBattle(roomCode, winnerId, reason);
            
            if (roomInfo != null) {
                logger.info("✅ 对战结束处理完成，房间: {}", roomCode);
                return JsonResponse.success(roomInfo);
            } else {
                return JsonResponse.failure("结束对战失败，房间可能不存在或状态不正确");
            }
            
        } catch (Exception e) {
            logger.error("❌ 结束对战失败: {}", e.getMessage(), e);
            return JsonResponse.failure("结束对战失败: " + e.getMessage());
        }
    }

    /**
     * 获取对战状态
     * 
     * 接口说明：
     * 1. 根据房间码获取当前对战状态
     * 2. 包含双方的提交状态、对战时间等信息
     * 3. 用于实时同步对战状态
     * 
     * @param roomCode 房间码
     * @return 对战状态信息
     */
    @GetMapping("/status/{roomCode}")
    @ApiOperation(value = "获取对战状态", notes = "获取当前对战的实时状态")
    public JsonResponse<Map<String, Object>> getBattleStatus(
            @ApiParam(value = "房间码", required = true, example = "123456")
            @PathVariable Long roomCode) {
        
        logger.debug("📋 获取对战状态，房间: {}", roomCode);
        
        try {
            // 参数验证
            if (roomCode == null) {
                return JsonResponse.failure("房间码不能为空");
            }
            
            // 获取房间信息
            RoomInfoDTO roomInfo = roomService.getRoomInfo(roomCode);
            
            if (roomInfo == null) {
                return JsonResponse.failure("房间不存在");
            }
            
            // 构建对战状态数据
            Map<String, Object> battleStatus = Map.of(
                "roomCode", roomCode,
                "status", roomInfo.getStatus(),
                "participants", roomInfo.getParticipants(),
                "problem", roomInfo.getProblem(),
                "startTime", roomInfo.getStartTime(),
                "endTime", roomInfo.getEndTime()
            );
            
            return JsonResponse.success(battleStatus);
            
        } catch (Exception e) {
            logger.error("❌ 获取对战状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("获取对战状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查Python服务状态
     * 
     * @return Python服务是否可用
     */
    @GetMapping("/python-service/status")
    @ApiOperation(value = "检查Python服务状态", notes = "检查Python服务是否可用")
    public JsonResponse<Map<String, Object>> checkPythonServiceStatus() {
        
        try {
            boolean isAvailable = pythonServiceUtils.isServiceAvailable();
            
            Map<String, Object> statusData = Map.of(
                "isAvailable", isAvailable,
                "checkTime", System.currentTimeMillis()
            );
            
            return JsonResponse.success(statusData);
            
        } catch (Exception e) {
            logger.error("❌ 检查Python服务状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("检查Python服务状态失败: " + e.getMessage());
        }
    }
}
