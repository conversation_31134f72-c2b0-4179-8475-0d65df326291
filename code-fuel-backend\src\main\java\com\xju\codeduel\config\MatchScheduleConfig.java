package com.xju.codeduel.config;

import com.xju.codeduel.service.IMatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 匹配相关定时任务配置
 * 
 * 功能说明：
 * 1. 定期清理匹配队列
 * 2. 处理匹配超时
 * 3. 优化匹配性能
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Configuration
@EnableScheduling
public class MatchScheduleConfig {

    private static final Logger logger = LoggerFactory.getLogger(MatchScheduleConfig.class);

    @Autowired
    private IMatchService matchService;

    /**
     * 每30秒检查一次匹配超时
     */
    @Scheduled(fixedRate = 30000)
    public void handleMatchTimeout() {
        try {
            matchService.handleMatchTimeout();
        } catch (Exception e) {
            logger.error("❌ 处理匹配超时失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每5分钟清理一次匹配队列
     */
    @Scheduled(fixedRate = 300000)
    public void cleanupMatchQueue() {
        try {
            matchService.cleanupMatchQueue();
        } catch (Exception e) {
            logger.error("❌ 清理匹配队列失败: {}", e.getMessage(), e);
        }
    }
}
