<template>
  <div class="chat-container">
    <!-- 聊天区头部 -->
    <el-card class="chat-header">
      <div class="header-content">
        <h2>
          <el-icon><ChatDotRound /></el-icon>
          聊天区
        </h2>
        <div class="header-actions">
          <el-button type="primary" size="default" @click="fetchMessages" :loading="loading">
            <el-icon><Promotion /></el-icon>
            刷新消息
          </el-button>
          <el-button type="danger" size="default" @click="clearAllMessages">
            <el-icon><Delete /></el-icon>
            清空聊天记录
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 聊天消息区域 -->
    <el-card class="chat-messages" v-loading="loading">
      <div
          class="messages-container"
          ref="messagesContainer"
          @wheel.stop
          @scroll.passive>
        <div v-if="messages.length === 0" class="empty-messages">
          <el-empty description="暂无聊天消息" />
        </div>

        <div v-for="message in messages" :key="message.id" class="message-item">
          <div class="message-header">
            <el-avatar :size="32" :src="message.user.avatar" />
            <div class="message-info">
              <span class="username">{{ message.user.codeforcesId }}</span>
              <span class="rating" v-if="message.user.rating">({{ message.user.rating }})</span>
              <span class="time">{{ formatTime(message.sentTime) }}</span>
            </div>
            <el-dropdown @command="handleMessageAction" v-if="canDeleteMessage(message)">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`delete-${message.id}`" style="color: #F56C6C">
                    <el-icon><Delete /></el-icon>
                    删除消息
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="message-content" :class="{ 'deleted': message.isDeleted }">
            {{ message.isDeleted ? '[消息已删除]' : message.content }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 消息输入区域 -->
    <el-card class="message-input">
      <div class="input-container">
        <el-input
            v-model="newMessage"
            type="textarea"
            :rows="3"
            placeholder="输入消息内容..."
            maxlength="500"
            show-word-limit
            @keydown.ctrl.enter="sendMessage"
        />
        <div class="input-actions">
          <span class="tip">Ctrl + Enter 发送</span>
          <el-button type="primary" @click="sendMessage" :disabled="!newMessage.trim()">
            <el-icon><Promotion /></el-icon>
            发送
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  Delete,
  MoreFilled,
  Promotion
} from '@element-plus/icons-vue'
import { useUserInfoStore } from '@/stores/userInfo'
import { getInfo, getChatMessages, sendChatMessage, deleteChatMessage } from '@/api/api'

// 响应式数据
const loading = ref(false)
const newMessage = ref('')
const messagesContainer = ref(null)
const userInfoStore = useUserInfoStore()

// 消息列表
const messages = ref([])

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await getInfo()
    if (res.data == null) {
      ElMessage.error("暂未登录，请先登录")
      // 可以根据需要跳转到登录页面
    } else {
      userInfoStore.setUserInfo(res.data)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 方法
const formatTime = (time) => {
  const now = new Date()
  const messageTime = new Date(time)
  const diff = now - messageTime
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  const days = Math.floor(hours / 24)
  if (days < 7) return `${days}天前`
  return messageTime.toLocaleDateString() + ' ' + messageTime.toLocaleTimeString()
}

const canDeleteMessage = (message) => {
  // 只有消息发送者或管理员可以删除消息
  return message.userId === userInfoStore.userInfo.id || userInfoStore.userInfo.isAdmin === 1
}

const sendMessage = async () => {
  if (!newMessage.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }

  if (!userInfoStore.userInfo.id) {
    ElMessage.error('用户信息获取失败，请重新登录')
    return
  }

  try {
    loading.value = true

    // 调用真实的发送消息API
    const response = await sendChatMessage({
      userId: userInfoStore.userInfo.id,
      content: newMessage.value.trim()
    })

    if (response.status) {
      // 发送成功后重新获取消息列表
      await fetchMessages()
      newMessage.value = ''

      // 滚动到底部
      await nextTick()
      scrollToBottom()

      ElMessage.success('消息发送成功')
    } else {
      throw new Error(response.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error(error.message || '消息发送失败')
  } finally {
    loading.value = false
  }
}

const handleMessageAction = (command) => {
  const [action, messageId] = command.split('-')

  if (action === 'delete') {
    deleteMessage(parseInt(messageId))
  }
}

const deleteMessage = async (messageId) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const params = {
      userId: userInfoStore.userInfo.id,
      isAdmin: userInfoStore.userInfo.isAdmin === 1
    }

    const response = await deleteChatMessage(messageId, params)

    if (response.status) {
      ElMessage.success('消息删除成功')
      await fetchMessages() // 重新加载消息列表
    } else {
      ElMessage.error(response.message || '删除消息失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除
      return
    }
    console.error('删除消息失败:', error)
    ElMessage.error('删除消息失败')
  }
}

const clearAllMessages = () => {
  ElMessageBox.confirm('确定要清空所有聊天记录吗？此操作不可恢复！', '确认清空', {
    type: 'warning',
    confirmButtonText: '确定清空',
    cancelButtonText: '取消'
  }).then(async () => {
    try {
      loading.value = true

      // 模拟清空聊天记录API调用
      messages.value = []
      ElMessage.success('聊天记录已清空')
    } catch (error) {
      ElMessage.error('清空失败')
    } finally {
      loading.value = false
    }
  })
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 确保滚动容器可以正常滚动
const setupScrollContainer = () => {
  if (messagesContainer.value) {
    // 确保容器可以接收滚动事件
    messagesContainer.value.style.pointerEvents = 'auto'
    messagesContainer.value.style.touchAction = 'auto'

    // 强制设置滚动属性
    messagesContainer.value.style.overflowY = 'auto'
    messagesContainer.value.style.overflowX = 'hidden'

    // 添加调试信息
    console.log('滚动容器设置完成:', {
      scrollHeight: messagesContainer.value.scrollHeight,
      clientHeight: messagesContainer.value.clientHeight,
      canScroll: messagesContainer.value.scrollHeight > messagesContainer.value.clientHeight
    })
  }
}

// 获取聊天消息列表
const fetchMessages = async () => {
  try {
    loading.value = true
    const response = await getChatMessages()

    if (response.status) {
      // 转换后端数据格式以适应前端显示
      messages.value = response.data.map(item => ({
        id: item.message.id,
        userId: item.message.userId,
        content: item.message.content,
        sentTime: item.message.sentTime,
        isDeleted: item.message.isDeleted,
        user: {
          codeforcesId: item.user.codeforcesId,
          avatar: item.user.avatar,
          rating: item.user.rating
        }
      }))

      // 滚动到底部
      await nextTick()
      scrollToBottom()
      setupScrollContainer() // 确保滚动容器设置正确
    } else {
      throw new Error(response.message || '获取聊天记录失败')
    }
  } catch (error) {
    console.error('获取聊天记录失败:', error)

    // 如果API失败，添加一些测试消息以便测试滚动功能
    messages.value = [
      {
        id: 1,
        userId: 1,
        content: '这是第一条测试消息',
        sentTime: new Date().toISOString(),
        isDeleted: 0,
        user: { codeforcesId: 'test1', avatar: '', rating: 1500 }
      },
      {
        id: 2,
        userId: 2,
        content: '这是第二条测试消息，内容稍微长一些，用来测试消息显示效果',
        sentTime: new Date().toISOString(),
        isDeleted: 0,
        user: { codeforcesId: 'test2', avatar: '', rating: 1600 }
      },
      {
        id: 3,
        userId: 3,
        content: '这是第三条测试消息',
        sentTime: new Date().toISOString(),
        isDeleted: 0,
        user: { codeforcesId: 'test3', avatar: '', rating: 1700 }
      },
      {
        id: 4,
        userId: 4,
        content: '这是第四条测试消息，用来确保有足够的内容可以滚动',
        sentTime: new Date().toISOString(),
        isDeleted: 0,
        user: { codeforcesId: 'test4', avatar: '', rating: 1800 }
      },
      {
        id: 5,
        userId: 5,
        content: '这是第五条测试消息',
        sentTime: new Date().toISOString(),
        isDeleted: 0,
        user: { codeforcesId: 'test5', avatar: '', rating: 1900 }
      }
    ]

    await nextTick()
    setupScrollContainer()
    ElMessage.warning('使用测试数据，请检查API连接')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await getUserInfo()
  await fetchMessages()

  // 设置滚动容器
  await nextTick()
  setupScrollContainer()
})
</script>

<style lang="scss" scoped>
.chat-container {
  max-width: 1000px;
  margin: 0 auto;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  position: relative;

  .chat-header {
    margin-bottom: 12px;
    flex-shrink: 0;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        display: flex;
        align-items: center;
        margin: 0;

        .el-icon {
          margin-right: 10px;
          color: #409EFF;
        }
      }

      .header-actions {
        display: flex;
        gap: 15px;
        align-items: center;

        .el-button {
          padding: 8px 16px;
          font-size: 14px;
          height: auto;
          min-height: 36px;

          .el-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
      }
    }
  }

  .chat-messages {
    flex: 1;
    margin-bottom: 12px;
    overflow: hidden;
    min-height: 0; /* 确保flex子元素可以收缩 */

    // 重写 Element Plus 卡片的样式
    :deep(.el-card__body) {
      height: 100%;
      padding: 0;
      overflow: hidden;
    }

    .messages-container {
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 10px 20px;
      scroll-behavior: smooth; /* 平滑滚动 */
      max-height: calc(100vh - 250px); /* 设置最大高度 */
      position: relative;
      z-index: 1;
      /* 确保可以接收鼠标事件 */
      pointer-events: auto;
      /* 确保可以滚动 */
      -webkit-overflow-scrolling: touch;

      .empty-messages {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .message-item {
        margin-bottom: 15px;

        .message-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .message-info {
            margin-left: 10px;
            flex: 1;

            .username {
              font-weight: 600;
              color: #303133;
              margin-right: 10px;
            }

            .rating {
              color: #fbbc05;
              margin-right: 10px;
              font-size: 12px;
            }

            .time {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .message-content {
          margin-left: 42px;
          padding: 10px 15px;
          background: #f5f7fa;
          border-radius: 10px;
          line-height: 1.5;
          word-wrap: break-word;

          &.deleted {
            color: #999;
            font-style: italic;
            background: #f0f0f0;
          }
        }
      }
    }
  }

  .message-input {
    flex-shrink: 0;

    .input-container {
      .input-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .tip {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

// 滚动条样式
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
  margin: 5px 0;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.messages-container::-webkit-scrollbar-thumb:active {
  background: #999999;
}

// Firefox 滚动条样式
.messages-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>
