package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评论
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comments")
@ApiModel(value="Comments对象", description="评论")
public class Comments implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

        @ApiModelProperty(value = "帖子ID")
    @TableField("post_id")
    private Long postId;

        @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

        @ApiModelProperty(value = "父评论ID")
    @TableField("parent_id")
    private Long parentId;

        @ApiModelProperty(value = "评论内容")
    @TableField("content")
    private String content;

        @ApiModelProperty(value = "评论时间")
    @TableField("create_time")
    private LocalDateTime createTime;

        @ApiModelProperty(value = "逻辑删除(0正常，1删除)")
    @TableField("is_deleted")
        @TableLogic
    private Integer isDeleted;


}
