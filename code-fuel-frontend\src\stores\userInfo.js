import {defineStore} from "pinia";
import {ref} from "vue";

export const useUserInfoStore = defineStore('userInfo',
    () => {
    // 定义变量
      const userInfo = ref({})

        // 设置值
      const setUserInfo = (newUserInfo) => {
        userInfo.value = newUserInfo
      }

      // 清理值
      const removeUserInfo = () => {
        userInfo.value = {}
        // 清除所有可能的本地存储
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        sessionStorage.removeItem('userInfo')
        sessionStorage.removeItem('token')
        // 清除Pinia持久化存储
        localStorage.removeItem('userInfo')
      }

      return {
        userInfo, setUserInfo, removeUserInfo
      }
    },
    {
      persist: true
    });