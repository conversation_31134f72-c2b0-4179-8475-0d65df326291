package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IProblemsService;
import com.xju.codeduel.model.domain.Problems;
import java.util.List;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/problems")
public class ProblemsController {

    private final Logger logger = LoggerFactory.getLogger( ProblemsController.class );

    @Autowired
    private IProblemsService problemsService;


    /**
     * 根据难度和标签筛选题目
     *
     * 功能说明：
     * 1. 根据难度范围筛选题目
     * 2. 排除指定的标签
     * 3. 用于房间对战时的题目选择
     *
     * @param minDifficulty 最小难度分数
     * @param maxDifficulty 最大难度分数
     * @param excludedTags 排除的标签ID列表（逗号分隔）
     * @return 筛选后的题目列表
     */
    @RequestMapping(value = "/filter", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<List<Problems>> filterProblems(
            @RequestParam(value = "minDifficulty", defaultValue = "800") Integer minDifficulty,
            @RequestParam(value = "maxDifficulty", defaultValue = "2000") Integer maxDifficulty,
            @RequestParam(value = "excludedTags", required = false) String excludedTags) {

        logger.info("🔍 筛选题目，难度范围: {}-{}, 排除标签: {}", minDifficulty, maxDifficulty, excludedTags);

        try {
            // 参数验证
            if (minDifficulty < 0 || maxDifficulty < 0 || minDifficulty > maxDifficulty) {
                return JsonResponse.failure("难度参数不正确");
            }

            // 解析排除的标签ID
            List<Long> excludedTagIds = null;
            if (excludedTags != null && !excludedTags.trim().isEmpty()) {
                try {
                    excludedTagIds = java.util.Arrays.stream(excludedTags.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(java.util.stream.Collectors.toList());
                } catch (NumberFormatException e) {
                    return JsonResponse.failure("标签ID格式不正确");
                }
            }

            // 调用服务层筛选题目
            List<Problems> filteredProblems = problemsService.filterProblems(minDifficulty, maxDifficulty, excludedTagIds);

            logger.info("✅ 筛选到 {} 道题目", filteredProblems.size());
            return JsonResponse.success(filteredProblems);

        } catch (Exception e) {
            logger.error("❌ 筛选题目失败: {}", e.getMessage(), e);
            return JsonResponse.failure("筛选题目失败: " + e.getMessage());
        }
    }

    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Problems> getById(@PathVariable("id") Long id)throws Exception {
        Problems problems = problemsService.getById(id);
        return JsonResponse.success(problems);
    }
}

