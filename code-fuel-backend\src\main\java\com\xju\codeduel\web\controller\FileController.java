package com.xju.codeduel.web.controller;


import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/file")
public class FileController {
    @Autowired
    private FileService fileService;
    @RequestMapping("/upload")
    public JsonResponse upload(@RequestParam("file") MultipartFile file) {
        Map upload = null;
        try {
            upload = fileService.upload(file);
            return JsonResponse.success(upload);
        } catch (IOException e) {
            System.out.println(e);
//            throw new RuntimeException(e);
        }
        return JsonResponse.failure("上传失败");
    }

}
