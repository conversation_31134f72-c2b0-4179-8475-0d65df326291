package com.xju.codeduel.service.impl;

import com.xju.codeduel.model.domain.Problems;
import com.xju.codeduel.mapper.ProblemsMapper;
import com.xju.codeduel.service.IProblemsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

/**
 * <p>
 * 题目信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class ProblemsServiceImpl extends ServiceImpl<ProblemsMapper, Problems> implements IProblemsService {

    private static final Logger logger = LoggerFactory.getLogger(ProblemsServiceImpl.class);

    @Override
    public List<Problems> filterProblems(Integer minDifficulty, Integer maxDifficulty, List<Long> excludedTagIds) {
        logger.info("🔍 开始筛选题目，难度范围: {}-{}, 排除标签: {}", minDifficulty, maxDifficulty, excludedTagIds);

        try {
            // 构建查询条件
            LambdaQueryWrapper<Problems> queryWrapper = new LambdaQueryWrapper<>();

            // 难度范围筛选
            if (minDifficulty != null) {
                queryWrapper.ge(Problems::getDifficulty, minDifficulty);
            }
            if (maxDifficulty != null) {
                queryWrapper.le(Problems::getDifficulty, maxDifficulty);
            }

            // 排除已删除的题目
            queryWrapper.eq(Problems::getIsDeleted, 0);

            // 获取初步筛选结果
            List<Problems> problems = this.list(queryWrapper);

            // 如果有排除的标签，需要进一步筛选
            if (excludedTagIds != null && !excludedTagIds.isEmpty()) {
                // TODO: 实现基于problems_tags表的标签筛选
                // 这里暂时返回所有题目，后续可以通过联表查询实现
                logger.info("⚠️ 标签筛选功能暂未实现，返回所有符合难度条件的题目");
            }

            logger.info("✅ 筛选完成，共找到 {} 道符合条件的题目", problems.size());
            return problems;

        } catch (Exception e) {
            logger.error("❌ 筛选题目失败: {}", e.getMessage(), e);
            throw new RuntimeException("筛选题目失败: " + e.getMessage());
        }
    }

}
