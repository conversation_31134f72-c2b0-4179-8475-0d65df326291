import { createRouter, createWebHistory } from "vue-router";
import Login from "@/views/Login.vue";
import Admin from "@/views/Admin.vue";
import UserInfo from "@/views/admin/UserInfo.vue";
import UserList from "@/views/admin/UserList.vue";
import DataManagement from "@/views/admin/DataManagement.vue";
import ProblemManagement from "@/views/admin/ProblemManagement.vue";
import TagManagement from "@/views/admin/TagManagement.vue";
import RestPassword from "@/views/user/RestPassword.vue";
import Dashboard from "@/views/Dashboard.vue";
import Home from "@/views/dashboard/Home.vue";
import Forum from "@/views/dashboard/Forum.vue";
import Battle from "@/views/dashboard/Battle.vue";
import Ranking from "@/views/dashboard/Ranking.vue";
import Profile from "@/views/dashboard/Profile.vue";
import Chat from "@/views/dashboard/Chat.vue";
import PostDetail from "@/views/dashboard/PostDetail.vue";

import test from "@/views/user/Test.vue";
import { getCurrentRoomStatus, getRoomInfo, leaveCurrentRoom } from "@/api/api";
import { useUserInfoStore } from "@/stores/userInfo";

// 定义路由关系
const routes = [
  // 根路径重定向到dashboard/home
  { path: '/', redirect: '/dashboard/home' },

  // 登录页面
  { path: '/login', component: Login },

  // Dashboard相关路由
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: 'home', component: Home },
      { path: 'forum', component: Forum },
      { path: 'forum/post/:id', component: PostDetail },
      { path: 'battle', component: Battle },
      { path: 'ranking', component: Ranking },
      { path: 'chat', component: Chat },
      { path: 'profile/:username', component: Profile },
      { path: 'user/info', component: UserInfo }
    ]
  },

  // Admin相关路由
  {
    path: '/admin',
    component: Admin,
    children: [
      { path: 'userlist', component: UserList },
      { path: 'userinfo', component: UserInfo },
      { path: 'data-management', component: DataManagement },
      { path: 'problem-management', component: ProblemManagement },
      { path: 'tag-management', component: TagManagement }
    ]
  },

  // User相关路由
  { path: '/user/resetPassword', component: RestPassword },


  // 测试路由
  { path: '/test', component: test }

]


// 创建路由器
const router = createRouter({
  history: createWebHistory(), // 路由模式
  routes: routes
})

// 路由守卫：检查登录状态和房间状态
router.beforeEach(async (to, from, next) => {
  try {
    // 如果是跳转到登录页面，直接允许（退出登录时需要）
    if (to.path === '/login') {
      next()
      return
    }

    // 检查登录状态
    const userInfoStore = useUserInfoStore()
    if (!userInfoStore.userInfo || !userInfoStore.userInfo.id) {
      console.log('🔒 用户未登录，重定向到登录页面')
      next('/login')
      return
    }

    // 检查用户当前房间状态
    const response = await getCurrentRoomStatus()

    if (response.status && response.data && response.data.hasRoom) {
      const userRoomCode = response.data.roomCode

      // 验证房间是否真的存在
      try {
        const roomResponse = await getRoomInfo(userRoomCode)

        if (!roomResponse.status || !roomResponse.data) {
          // 房间不存在，清除用户状态
          console.log(`🧹 房间 ${userRoomCode} 不存在，清除用户状态`)
          await leaveCurrentRoom()

          // 重定向到对战页面
          next('/dashboard/battle')
          return
        }

        // 房间存在，用户在房间中，强制重定向到对战页面（会显示房间弹窗）
        if (to.path !== '/dashboard/battle') {
          console.log(`🔄 用户在房间 ${userRoomCode} 中，强制重定向到对战页面`)
          next('/dashboard/battle')
          return
        }

      } catch (roomError) {
        // 房间检查失败，清除用户状态
        console.log(`❌ 检查房间 ${userRoomCode} 失败，清除用户状态`)
        try {
          await leaveCurrentRoom()
        } catch (leaveError) {
          console.error('❌ 清除用户状态失败:', leaveError)
        }

        // 重定向到对战页面
        next('/dashboard/battle')
        return
      }
    }

    // 正常导航
    next()

  } catch (error) {
    console.error('❌ 路由守卫检查失败:', error)
    // 出错时允许正常导航，避免阻塞用户
    next()
  }
})

//导出暴露
export default router
