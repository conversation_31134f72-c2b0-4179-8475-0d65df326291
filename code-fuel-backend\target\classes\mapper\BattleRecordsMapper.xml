<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.BattleRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.BattleRecords">
        <id column="id" property="id" />
        <result column="problem_id" property="problemId" />
        <result column="is_room" property="isRoom" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="room_code" property="roomCode" />
    </resultMap>

    <!-- Problems结果映射 -->
    <resultMap id="ProblemsResultMap" type="com.xju.codeduel.model.domain.Problems">
        <id column="p_id" property="id"/>
        <result column="p_title" property="title"/>
        <result column="p_difficulty" property="difficulty"/>
        <result column="p_contest_id" property="contestId"/>
        <result column="p_problem_id" property="problemId"/>
        <result column="p_created_time" property="createdTime"/>
        <result column="p_update_time" property="updateTime"/>
        <result column="p_is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- Users结果映射 -->
    <resultMap id="UsersResultMap" type="com.xju.codeduel.model.domain.Users">
        <id column="u_id" property="id"/>
        <result column="u_password" property="password"/>
        <result column="u_codeforces_id" property="codeforcesId"/>
        <result column="u_avatar" property="avatar"/>
        <result column="u_rating" property="rating"/>
        <result column="u_status" property="status"/>
        <result column="u_ban_reason" property="banReason"/>
        <result column="u_register_time" property="registerTime"/>
        <result column="u_update_time" property="updateTime"/>
        <result column="u_is_admin" property="isAdmin"/>
        <result column="u_last_login" property="lastLogin"/>
    </resultMap>

    <!-- BattleRecordWithDetailsDTO结果映射 -->
    <resultMap id="BattleRecordWithDetailsResultMap" type="com.xju.codeduel.model.dto.BattleRecordWithDetailsDTO">
        <association property="battleRecord" resultMap="BaseResultMap"/>
        <association property="problem" resultMap="ProblemsResultMap"/>
        <collection property="participants" resultMap="UsersResultMap"/>
    </resultMap>

    <!-- 获取最近的对战记录 -->
    <select id="getRecentBattleRecords" resultMap="BattleRecordWithDetailsResultMap">
        SELECT
            br.id, br.problem_id, br.is_room, br.start_time, br.end_time, br.room_code,
            p.id as p_id, p.title as p_title, p.difficulty as p_difficulty,
            p.contest_id as p_contest_id, p.problem_id as p_problem_id,
            p.created_time as p_created_time, p.update_time as p_update_time, p.is_deleted as p_is_deleted,
            u.id as u_id, u.password as u_password, u.codeforces_id as u_codeforces_id,
            u.avatar as u_avatar, u.rating as u_rating, u.status as u_status,
            u.ban_reason as u_ban_reason, u.register_time as u_register_time,
            u.update_time as u_update_time, u.is_admin as u_is_admin, u.last_login as u_last_login
        FROM battle_records br
        LEFT JOIN problems p ON br.problem_id = p.id
        LEFT JOIN user_battle_record ubr ON br.id = ubr.battle_id
        LEFT JOIN users u ON ubr.user_id = u.id
        ORDER BY br.start_time DESC
    </select>

</mapper>
