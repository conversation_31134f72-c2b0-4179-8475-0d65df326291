package com.xju.codeduel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 创建房间请求DTO
 * 用于接收前端创建房间的请求数据
 * 
 * 功能说明：
 * 1. 封装创建房间所需的基本信息
 * 2. 包含房间创建者信息和题目选择
 * 3. 提供数据验证和类型安全
 * 
 * 使用场景：
 * - 用户点击"创建房间"时发送此DTO到后端
 * - 后端根据此DTO创建新的对战房间
 * - 生成唯一房间码供其他用户加入
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@ApiModel(value = "RoomCreateDTO", description = "创建房间请求数据")
public class RoomCreateDTO {

    @ApiModelProperty(value = "创建者用户ID", required = true, example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "创建者用户名", required = true, example = "tourist")
    private String creatorName;

    @ApiModelProperty(value = "题目ID（可选，不指定则随机选择）", example = "1")
    private Long problemId;

    @ApiModelProperty(value = "房间描述（可选）", example = "欢迎来到我的房间")
    private String description;

    @ApiModelProperty(value = "最小难度分数", example = "800")
    private Integer minDifficulty;

    @ApiModelProperty(value = "最大难度分数", example = "2000")
    private Integer maxDifficulty;

    @ApiModelProperty(value = "排除的标签ID列表")
    private java.util.List<Long> excludedTags;

    /**
     * 默认构造函数
     */
    public RoomCreateDTO() {}

    /**
     * 全参构造函数
     *
     * @param creatorId 创建者用户ID
     * @param creatorName 创建者用户名
     * @param problemId 题目ID
     * @param description 房间描述
     * @param minDifficulty 最小难度分数
     * @param maxDifficulty 最大难度分数
     * @param excludedTags 排除的标签ID列表
     */
    public RoomCreateDTO(Long creatorId, String creatorName, Long problemId, String description,
                        Integer minDifficulty, Integer maxDifficulty, java.util.List<Long> excludedTags) {
        this.creatorId = creatorId;
        this.creatorName = creatorName;
        this.problemId = problemId;
        this.description = description;
        this.minDifficulty = minDifficulty;
        this.maxDifficulty = maxDifficulty;
        this.excludedTags = excludedTags;
    }

    // ==================== Getter和Setter方法 ====================

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getProblemId() {
        return problemId;
    }

    public void setProblemId(Long problemId) {
        this.problemId = problemId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getMinDifficulty() {
        return minDifficulty;
    }

    public void setMinDifficulty(Integer minDifficulty) {
        this.minDifficulty = minDifficulty;
    }

    public Integer getMaxDifficulty() {
        return maxDifficulty;
    }

    public void setMaxDifficulty(Integer maxDifficulty) {
        this.maxDifficulty = maxDifficulty;
    }

    public java.util.List<Long> getExcludedTags() {
        return excludedTags;
    }

    public void setExcludedTags(java.util.List<Long> excludedTags) {
        this.excludedTags = excludedTags;
    }

    /**
     * 重写toString方法，便于日志记录和调试
     * 注意：不包含敏感信息
     */
    @Override
    public String toString() {
        return "RoomCreateDTO{" +
                "creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", problemId=" + problemId +
                ", description='" + description + '\'' +
                ", minDifficulty=" + minDifficulty +
                ", maxDifficulty=" + maxDifficulty +
                ", excludedTags=" + excludedTags +
                '}';
    }
}
