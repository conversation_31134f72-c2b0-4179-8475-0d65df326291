package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xju.codeduel.mapper.UsersMapper;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.service.IRoomService;
import com.xju.codeduel.service.UserRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户房间状态服务实现
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class UserRoomServiceImpl implements UserRoomService {

    private static final Logger logger = LoggerFactory.getLogger(UserRoomServiceImpl.class);

    @Autowired
    private UsersMapper usersMapper;

    @Autowired
    @Lazy
    private IRoomService roomService;

    // 注意：为了避免循环依赖，这里不直接注入RoomService
    // 而是通过静态缓存直接访问房间信息
    private static final Map<Long, RoomInfoDTO> roomCache = new HashMap<>();

    @Override
    public Map<String, Object> getCurrentRoomStatus(Long userId) {
        logger.info("🔍 获取用户 {} 的当前房间状态", userId);
        
        Map<String, Object> result = new HashMap<>();
        
        // 查询用户当前房间状态
        Users user = usersMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        Long currentRoomCode = user.getCurrentRoomCode();
        String roomStatus = user.getRoomStatus();

        if (currentRoomCode == null || "NONE".equals(roomStatus)) {
            // 用户当前没有房间
            result.put("hasRoom", false);
            result.put("roomCode", null);
            result.put("status", "NONE");
            result.put("message", "当前没有参与任何房间");
        } else {
            // 用户有房间，返回房间信息
            result.put("hasRoom", true);
            result.put("roomCode", currentRoomCode);
            result.put("status", roomStatus);
            result.put("message", getStatusMessage(roomStatus));
        }

        logger.info("✅ 用户 {} 房间状态: {}", userId, result);
        return result;
    }

    @Override
    public Map<String, Object> rejoinRoom(Long userId, Long roomCode) {
        logger.info("🔄 用户 {} 尝试重新加入房间 {}", userId, roomCode);

        // 暂时简化实现，直接更新用户状态
        // TODO: 后续添加房间验证逻辑

        // 更新用户房间状态
        updateUserRoomStatus(userId, roomCode, "WAITING");

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("roomCode", roomCode);
        result.put("status", "WAITING");
        result.put("message", "成功重新加入房间");

        logger.info("✅ 用户 {} 成功重新加入房间 {}", userId, roomCode);
        return result;
    }

    @Override
    public void leaveRoom(Long userId) {
        logger.info("🚪 用户 {} 离开房间", userId);

        Users user = usersMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 获取用户当前房间码
        Long roomCode = user.getCurrentRoomCode();
        if (roomCode == null) {
            logger.info("⚠️ 用户 {} 当前没有房间", userId);
            return;
        }

        // 调用房间服务的离开房间方法（这会处理房间解散逻辑）
        try {
            roomService.leaveRoom(roomCode, userId);
            logger.info("✅ 用户 {} 已从房间 {} 中移除", userId, roomCode);
        } catch (Exception e) {
            logger.error("❌ 从房间中移除用户失败: {}", e.getMessage());
            // 即使房间操作失败，也要清除用户状态
            clearUserRoomStatus(userId);
        }

        logger.info("✅ 用户 {} 已离开房间", userId);
    }

    @Override
    public void updateUserRoomStatus(Long userId, Long roomCode, String status) {
        logger.info("📝 更新用户 {} 房间状态: 房间={}, 状态={}", userId, roomCode, status);

        UpdateWrapper<Users> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", userId)
                    .set("current_room_code", roomCode)
                    .set("room_status", status);

        usersMapper.update(null, updateWrapper);

        logger.info("✅ 用户 {} 房间状态已更新", userId);
    }

    @Override
    public void clearUserRoomStatus(Long userId) {
        logger.info("🧹 清除用户 {} 的房间状态", userId);

        UpdateWrapper<Users> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", userId)
                    .set("current_room_code", null)
                    .set("room_status", "NONE");

        usersMapper.update(null, updateWrapper);

        logger.info("✅ 用户 {} 房间状态已清除", userId);
    }

    @Override
    public boolean isUserInRoom(Long userId) {
        logger.debug("🔍 检查用户 {} 是否在房间中", userId);

        Users user = usersMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        Long currentRoomCode = user.getCurrentRoomCode();
        String roomStatus = user.getRoomStatus();

        boolean inRoom = currentRoomCode != null && !"NONE".equals(roomStatus);

        logger.debug("✅ 用户 {} 房间状态检查结果: {}", userId, inRoom ? "在房间中" : "不在房间中");
        return inRoom;
    }

    /**
     * 获取状态描述信息
     */
    private String getStatusMessage(String status) {
        switch (status) {
            case "WAITING":
                return "等待对手加入";
            case "READY":
                return "准备开始对战";
            case "BATTLING":
                return "对战进行中";
            case "NONE":
            default:
                return "当前没有参与任何房间";
        }
    }
}
