package com.xju.codeduel.model.dto;

import com.xju.codeduel.model.domain.BattleRecords;
import com.xju.codeduel.model.domain.Problems;
import com.xju.codeduel.model.domain.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "BattleRecordWithDetailsDTO对象", description = "包含对战记录详细信息的DTO")
public class BattleRecordWithDetailsDTO {
    @ApiModelProperty(value = "对战记录信息")
    private BattleRecords battleRecord;

    @ApiModelProperty(value = "题目信息")
    private Problems problem;

    @ApiModelProperty(value = "参与对战的用户列表")
    private List<Users> participants;
}
