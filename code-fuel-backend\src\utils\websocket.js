import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'
import { ElMessage } from 'element-plus'

/**
 * WebSocket服务类
 * 
 * 功能说明：
 * 1. 管理WebSocket连接的生命周期
 * 2. 提供房间消息的订阅和发送功能
 * 3. 实现自动重连和错误处理机制
 * 4. 支持消息去重和状态管理
 * 
 * 使用方式：
 * import { webSocketService } from '@/utils/websocket'
 * 
 * // 连接WebSocket
 * await webSocketService.connect()
 * 
 * // 订阅房间消息
 * webSocketService.subscribeToRoom(roomCode, (message) => {
 *   console.log('收到消息:', message)
 * })
 * 
 * // 发送加入房间消息
 * webSocketService.sendJoinRoom(roomCode, userInfo)
 */
class WebSocketService {
  constructor() {
    this.client = null
    this.connected = false
    this.connecting = false
    this.subscriptions = new Map()
    this.messageHandlers = new Map()
    
    // 重连配置
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 5000
    
    // 消息去重
    this.messageIds = new Set()
    this.maxMessageIds = 1000
    
    // 服务器地址配置
    this.serverUrl = import.meta.env.VITE_WS_URL || 'http://localhost:8080'
  }

  /**
   * 连接WebSocket服务器
   * 
   * @returns {Promise} 连接结果
   */
  connect() {
    return new Promise((resolve, reject) => {
      if (this.connecting) {
        reject(new Error('WebSocket正在连接中'))
        return
      }

      if (this.connected) {
        resolve('WebSocket已连接')
        return
      }

      this.connecting = true

      try {
        this.client = new Client({
          // 使用SockJS作为传输层，兼容性更好
          webSocketFactory: () => new SockJS(`${this.serverUrl}/ws`),
          
          // 连接成功回调
          onConnect: (frame) => {
            console.log('✅ WebSocket连接成功:', frame)
            this.connected = true
            this.connecting = false
            this.reconnectAttempts = 0
            
            ElMessage.success('实时通信连接成功')
            resolve(frame)
          },
          
          // 连接失败回调
          onStompError: (frame) => {
            console.error('❌ WebSocket连接失败:', frame)
            this.connected = false
            this.connecting = false
            
            ElMessage.error('实时通信连接失败')
            reject(new Error(`WebSocket连接失败: ${frame.headers?.message || '未知错误'}`))
          },
          
          // 断开连接回调
          onDisconnect: () => {
            console.log('🔌 WebSocket连接断开')
            this.connected = false
            this.connecting = false
            
            // 自动重连
            this.scheduleReconnect()
          },
          
          // WebSocket错误回调
          onWebSocketError: (error) => {
            console.error('❌ WebSocket错误:', error)
            this.connected = false
            this.connecting = false
          },
          
          // 心跳设置（毫秒）
          heartbeatIncoming: 4000,
          heartbeatOutgoing: 4000,
          
          // 调试模式
          debug: (str) => {
            if (import.meta.env.DEV) {
              console.log('🔍 WebSocket调试:', str)
            }
          }
        })
        
        // 激活连接
        this.client.activate()
        
      } catch (error) {
        console.error('❌ 创建WebSocket客户端失败:', error)
        this.connecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.client && this.connected) {
      console.log('🔌 主动断开WebSocket连接')
      
      // 取消所有订阅
      this.subscriptions.forEach((subscription, roomCode) => {
        this.unsubscribeFromRoom(roomCode)
      })
      
      // 断开连接
      this.client.deactivate()
      this.connected = false
      this.connecting = false
    }
  }

  /**
   * 计划重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ WebSocket重连次数已达上限，停止重连')
      ElMessage.error('实时通信连接失败，请刷新页面重试')
      return
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts) // 指数退避
    this.reconnectAttempts++

    console.log(`🔄 ${delay}ms后尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      this.reconnect()
    }, delay)
  }

  /**
   * 重连WebSocket
   */
  async reconnect() {
    if (this.connecting || this.connected) {
      return
    }

    try {
      await this.connect()
      
      // 重连成功后重新订阅所有房间
      const roomCodes = Array.from(this.messageHandlers.keys())
      for (const roomCode of roomCodes) {
        const handler = this.messageHandlers.get(roomCode)
        if (handler) {
          this.subscribeToRoom(roomCode, handler)
        }
      }
      
      console.log('✅ WebSocket重连成功，已重新订阅所有房间')
      
    } catch (error) {
      console.error('❌ WebSocket重连失败:', error)
    }
  }

  /**
   * 订阅房间消息
   * 
   * @param {number} roomCode 房间码
   * @param {function} callback 消息处理回调
   * @returns {object|null} 订阅对象
   */
  subscribeToRoom(roomCode, callback) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，无法订阅房间消息')
      return null
    }

    // 如果已经订阅了该房间，先取消订阅
    if (this.subscriptions.has(roomCode)) {
      this.unsubscribeFromRoom(roomCode)
    }

    const destination = `/topic/room/${roomCode}`
    
    try {
      const subscription = this.client.subscribe(destination, (message) => {
        try {
          const data = JSON.parse(message.body)
          
          // 消息去重
          const messageId = `${data.type}_${data.timestamp}_${roomCode}`
          if (this.isDuplicateMessage(messageId)) {
            console.log('🔄 忽略重复消息:', messageId)
            return
          }
          
          console.log('📨 收到房间消息:', data)
          callback(data)
          
        } catch (error) {
          console.error('❌ 解析房间消息失败:', error)
        }
      })

      this.subscriptions.set(roomCode, subscription)
      this.messageHandlers.set(roomCode, callback)
      
      console.log(`📡 已订阅房间 ${roomCode} 的消息`)
      return subscription
      
    } catch (error) {
      console.error('❌ 订阅房间消息失败:', error)
      return null
    }
  }

  /**
   * 取消订阅房间消息
   * 
   * @param {number} roomCode 房间码
   */
  unsubscribeFromRoom(roomCode) {
    const subscription = this.subscriptions.get(roomCode)
    if (subscription) {
      try {
        subscription.unsubscribe()
        this.subscriptions.delete(roomCode)
        this.messageHandlers.delete(roomCode)
        console.log(`📡 已取消订阅房间 ${roomCode} 的消息`)
      } catch (error) {
        console.error('❌ 取消订阅失败:', error)
      }
    }
  }

  /**
   * 发送消息到服务器
   * 
   * @param {string} destination 目标地址
   * @param {object} body 消息体
   * @returns {boolean} 发送是否成功
   */
  sendMessage(destination, body) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，消息发送失败')
      return false
    }

    try {
      this.client.publish({
        destination,
        body: JSON.stringify(body)
      })
      
      console.log(`📤 发送消息到 ${destination}:`, body)
      return true
      
    } catch (error) {
      console.error('❌ 发送WebSocket消息失败:', error)
      return false
    }
  }

  /**
   * 发送加入房间消息
   * 
   * @param {number} roomCode 房间码
   * @param {object} userInfo 用户信息
   */
  sendJoinRoom(roomCode, userInfo) {
    return this.sendMessage(`/app/room/${roomCode}/join`, {
      userId: userInfo.id,
      userName: userInfo.codeforcesId
    })
  }

  /**
   * 发送离开房间消息
   * 
   * @param {number} roomCode 房间码
   * @param {object} userInfo 用户信息
   */
  sendLeaveRoom(roomCode, userInfo) {
    return this.sendMessage(`/app/room/${roomCode}/leave`, {
      userId: userInfo.id,
      userName: userInfo.codeforcesId
    })
  }

  /**
   * 发送开始对战消息
   * 
   * @param {number} roomCode 房间码
   * @param {object} userInfo 用户信息
   */
  sendStartBattle(roomCode, userInfo) {
    return this.sendMessage(`/app/room/${roomCode}/start`, {
      userId: userInfo.id,
      userName: userInfo.codeforcesId
    })
  }

  /**
   * 检查是否为重复消息
   * 
   * @param {string} messageId 消息ID
   * @returns {boolean} 是否重复
   */
  isDuplicateMessage(messageId) {
    if (this.messageIds.has(messageId)) {
      return true
    }

    this.messageIds.add(messageId)
    
    // 限制内存使用
    if (this.messageIds.size > this.maxMessageIds) {
      const oldIds = Array.from(this.messageIds).slice(0, this.maxMessageIds / 2)
      oldIds.forEach(id => this.messageIds.delete(id))
    }

    return false
  }

  /**
   * 检查连接状态
   * 
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.connected
  }

  /**
   * 获取连接状态信息
   * 
   * @returns {object} 状态信息
   */
  getStatus() {
    return {
      connected: this.connected,
      connecting: this.connecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: this.subscriptions.size
    }
  }
}

// 创建全局WebSocket服务实例
export const webSocketService = new WebSocketService()

// 导出类供其他地方使用
export default WebSocketService
