<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Avatar,
  Trophy,
  Clock,
  DocumentCopy,
  Close,
  VideoPlay,
  Refresh
} from '@element-plus/icons-vue'
import { getRoomInfo, startBattle, leaveCurrentRoom } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'

// ==================== 组件属性和事件 ====================

/**
 * 组件属性定义
 * - roomInfo: 房间信息对象
 * - visible: 是否显示房间等待界面
 */
const props = defineProps({
  roomInfo: {
    type: Object,
    required: false,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件定义
 * - close: 关闭房间等待界面
 * - battle-start: 对战开始
 * - room-update: 房间信息更新
 */
const emit = defineEmits(['close', 'battle-start', 'room-update'])

// ==================== 响应式数据 ====================

const userInfoStore = useUserInfoStore()

// 房间信息（本地副本，用于实时更新）
const localRoomInfo = ref(props.roomInfo ? { ...props.roomInfo } : null)

// 加载状态
const loading = ref(false)
const startingBattle = ref(false)

// 定时器
let refreshTimer = null

// 当前用户信息
const currentUser = computed(() => userInfoStore.userInfo)

// 是否为房主
const isCreator = computed(() => {
  return currentUser.value && localRoomInfo.value.creator && 
         currentUser.value.id === localRoomInfo.value.creator.id
})

// 是否可以开始对战
const canStartBattle = computed(() => {
  return localRoomInfo.value.status === 'READY' && 
         localRoomInfo.value.participantCount >= 2 &&
         isCreator.value
})

// ==================== 核心业务方法 ====================

/**
 * 刷新房间信息
 * 
 * 功能说明：
 * 1. 从后端获取最新的房间信息
 * 2. 更新本地房间状态
 * 3. 检查是否有状态变化需要通知父组件
 */
const refreshRoomInfo = async () => {
  if (!localRoomInfo.value || !localRoomInfo.value.roomCode) return
  
  try {
    const response = await getRoomInfo(localRoomInfo.value.roomCode)

    if (response.status && response.data) {
      const oldStatus = localRoomInfo.value ? localRoomInfo.value.status : null
      localRoomInfo.value = response.data
      
      // 如果状态发生变化，通知父组件
      if (oldStatus !== response.data.status) {
        emit('room-update', response.data)
        
        // 如果对战开始，自动跳转
        if (response.data.status === 'BATTLING') {
          emit('battle-start', response.data)
        }
      }
      
    } else {
      // 只有在房间状态不是BATTLING时才提示房间关闭
      if (localRoomInfo.value.status !== 'BATTLING') {
        ElMessage.error('房间可能已关闭')
        emit('close')
      }
    }
    
  } catch (error) {
    console.error('❌ 刷新房间信息失败:', error)
  }
}

/**
 * 复制房间码到剪贴板
 */
const copyRoomCode = async () => {
  try {
    await navigator.clipboard.writeText(localRoomInfo.value.roomCode.toString())
    ElMessage.success('房间码已复制到剪贴板')
  } catch (error) {
    // 降级方案：使用传统方法
    const textArea = document.createElement('textarea')
    textArea.value = localRoomInfo.value.roomCode.toString()
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('房间码已复制到剪贴板')
  }
}

/**
 * 开始对战
 * 
 * 功能说明：
 * 1. 验证房间状态和权限
 * 2. 调用后端API开始对战
 * 3. 通知所有参与者对战开始
 */
const handleStartBattle = async () => {
  if (!canStartBattle.value) {
    ElMessage.warning('当前无法开始对战')
    return
  }
  
  startingBattle.value = true
  
  try {
    const response = await startBattle(localRoomInfo.value.roomCode)
    
    if (response.status && response.data) {
      ElMessage.success('对战开始！')
      localRoomInfo.value = response.data
      emit('battle-start', response.data)
    } else {
      ElMessage.error(response.message || '开始对战失败')
    }
    
  } catch (error) {
    console.error('❌ 开始对战失败:', error)
    ElMessage.error('开始对战失败: ' + error.message)
  } finally {
    startingBattle.value = false
  }
}

/**
 * 处理对话框关闭事件
 */
const handleDialogClose = (done) => {
  ElMessageBox.confirm(
    '确定要离开房间吗？',
    '确认离开',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    emit('close')
    done()
  }).catch(() => {
    // 用户取消关闭
  })
}

/**
 * 离开房间
 */
const handleLeaveRoom = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要离开房间吗？',
      '确认离开',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用后端API离开房间
    loading.value = true

    const response = await leaveCurrentRoom()

    if (response.status) {
      ElMessage.success('已离开房间')
      emit('close')
    } else {
      ElMessage.error(response.message || '离开房间失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 离开房间失败:', error)
      ElMessage.error('离开房间失败: ' + (error.message || '未知错误'))
    }
  } finally {
    loading.value = false
  }
}

/**
 * 格式化房间状态显示
 */
const formatRoomStatus = (status) => {
  const statusMap = {
    'WAITING': '等待中',
    'READY': '准备就绪',
    'BATTLING': '对战中',
    'FINISHED': '已结束'
  }
  return statusMap[status] || status
}

/**
 * 获取房间状态对应的标签类型
 */
const getRoomStatusType = (status) => {
  const typeMap = {
    'WAITING': 'info',
    'READY': 'success',
    'BATTLING': 'warning',
    'FINISHED': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 格式化时间显示
 */
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN')
}

// ==================== 生命周期钩子 ====================

onMounted(() => {
  // 开始定时刷新房间信息
  refreshTimer = setInterval(refreshRoomInfo, 3000) // 每3秒刷新一次

  // 延迟一点再开始刷新，确保组件完全初始化
  setTimeout(() => {
    refreshRoomInfo()
  }, 100)
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 监听props变化
watch(() => props.roomInfo, (newRoomInfo) => {
  if (newRoomInfo) {
    localRoomInfo.value = { ...newRoomInfo }
  }
}, { deep: true })

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (visible) {
    // 显示时延迟刷新，确保数据已初始化
    setTimeout(() => {
      refreshRoomInfo()
    }, 100)
    // 重新开始定时器
    if (!refreshTimer) {
      refreshTimer = setInterval(refreshRoomInfo, 3000)
    }
  } else {
    // 隐藏时停止定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
})
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="房间等待"
    width="700px"
    :before-close="handleDialogClose"
    :close-on-click-modal="false"
  >
    <div class="room-waiting" v-loading="loading">
      <!-- 房间基本信息 -->
      <el-card class="room-info-card" shadow="never">
        <template #header>
          <div class="room-header">
            <div class="room-title">
              <h3>房间 {{ localRoomInfo.roomCode }}</h3>
              <el-tag :type="getRoomStatusType(localRoomInfo.status)" size="large">
                {{ formatRoomStatus(localRoomInfo.status) }}
              </el-tag>
            </div>
            <div class="room-actions">
              <el-button
                size="small"
                @click="copyRoomCode"
                :icon="DocumentCopy"
              >
                复制房间码
              </el-button>
              <el-button 
                size="small" 
                @click="refreshRoomInfo"
                :icon="Refresh"
                :loading="loading"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="room-details">
          <div class="detail-item">
            <span class="label">房主：</span>
            <div class="creator-info">
              <el-avatar :src="localRoomInfo.creator?.avatar" :size="24" />
              <span>{{ localRoomInfo.creator?.codeforcesId }}</span>
            </div>
          </div>
          
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span>{{ formatTime(localRoomInfo.createTime) }}</span>
          </div>
          
          <div class="detail-item" v-if="localRoomInfo.description">
            <span class="label">房间描述：</span>
            <span>{{ localRoomInfo.description }}</span>
          </div>
          
          <div class="detail-item" v-if="localRoomInfo.problem">
            <span class="label">题目：</span>
            <span>{{ localRoomInfo.problem.title }}</span>
          </div>
        </div>
      </el-card>

      <!-- 参与者列表 -->
      <el-card class="participants-card" shadow="never">
        <template #header>
          <div class="participants-header">
            <h4>参与者 ({{ localRoomInfo.participantCount }}/{{ localRoomInfo.maxParticipants }})</h4>
          </div>
        </template>
        
        <div class="participants-list">
          <div 
            v-for="participant in localRoomInfo.participants" 
            :key="participant.id"
            class="participant-item"
            :class="{ 'is-creator': participant.id === localRoomInfo.creator?.id }"
          >
            <el-avatar :src="participant.avatar" :size="40" />
            <div class="participant-info">
              <div class="participant-name">
                {{ participant.codeforcesId }}
                <el-tag v-if="participant.id === localRoomInfo.creator?.id" size="small" type="warning">
                  房主
                </el-tag>
              </div>
              <div class="participant-rating">
                <el-icon><Trophy /></el-icon>
                <span>{{ participant.rating || 'Unrated' }}</span>
              </div>
            </div>
          </div>
          
          <!-- 空位占位符 -->
          <div 
            v-for="i in (localRoomInfo.maxParticipants - localRoomInfo.participantCount)" 
            :key="'empty-' + i"
            class="participant-item empty-slot"
          >
            <el-avatar :size="40">
              <el-icon><Avatar /></el-icon>
            </el-avatar>
            <div class="participant-info">
              <div class="participant-name">等待玩家加入...</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 等待提示 -->
      <div class="waiting-tips" v-if="localRoomInfo.status === 'WAITING'">
        <el-alert
          title="等待其他玩家加入"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请将房间码 <strong>{{ localRoomInfo.roomCode }}</strong> 分享给其他玩家</p>
            <p>当房间人数达到 {{ localRoomInfo.maxParticipants }} 人时即可开始对战</p>
          </template>
        </el-alert>
      </div>

      <!-- 准备就绪提示 -->
      <div class="ready-tips" v-if="localRoomInfo.status === 'READY'">
        <el-alert
          title="房间已准备就绪"
          type="success"
          :closable="false"
          show-icon
        >
          <template #default>
            <p v-if="isCreator">所有玩家已加入，您可以开始对战了！</p>
            <p v-else>等待房主开始对战...</p>
          </template>
        </el-alert>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleLeaveRoom" :icon="Close">
          离开房间
        </el-button>
        <el-button
          v-if="canStartBattle"
          type="primary"
          @click="handleStartBattle"
          :loading="startingBattle"
          :icon="VideoPlay"
        >
          开始对战
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.room-waiting {
  .room-info-card {
    margin-bottom: 20px;

    .room-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .room-title {
        display: flex;
        align-items: center;
        gap: 12px;

        h3 {
          margin: 0;
          color: #303133;
        }
      }

      .room-actions {
        display: flex;
        gap: 8px;
      }
    }

    .room-details {
      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
        }

        .creator-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .participants-card {
    margin-bottom: 20px;

    .participants-header {
      h4 {
        margin: 0;
        color: #303133;
      }
    }

    .participants-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .participant-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background: #fff;
        transition: all 0.3s;

        &.is-creator {
          border-color: #f56c6c;
          background: #fef0f0;
        }

        &.empty-slot {
          border-style: dashed;
          background: #f5f7fa;
          color: #909399;
        }

        .participant-info {
          flex: 1;

          .participant-name {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .participant-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #909399;

            .el-icon {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .waiting-tips,
  .ready-tips {
    margin-bottom: 20px;

    .el-alert {
      :deep(.el-alert__content) {
        p {
          margin: 4px 0;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            color: #409eff;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>
