<template>
  <div class="user-rating-history">
    <h1>用户Rating历史记录</h1>

    <!-- 搜索框 -->
    <div class="search-box">
      <input
          v-model="searchUsername"
          placeholder="输入用户名搜索（如：tourist、jiangly、<PERSON><PERSON>、Um_nik）"
          @input="searchUsers"
      />
      <button @click="clearSearch" class="clear-btn">清空</button>
    </div>

    <!-- 当前查看状态 -->
    <div v-if="!loading && searchUsername && processedUserList.length > 0" class="current-search">
      <p>正在查看用户: <strong>{{ searchUsername }}</strong> 的Rating历史记录</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>

    <!-- 用户列表 -->
    <div v-else class="user-list">
      <div v-for="user in processedUserList" :key="user.id" class="user-card">
        <div class="user-info">
          <img :src="user.avatar" alt="头像" class="avatar">
          <div class="info">
            <h3>{{ user.codeforcesId }}</h3>
            <p class="rating">当前Rating: <span :class="getRatingClass(user.rating)">{{ user.rating }}</span></p>
            <p>注册时间: {{ formatDate(user.registerTime) }}</p>
            <p>历史记录数: {{ user.totalHistories }}条</p>
          </div>
        </div>
        <button @click="showRatingHistory(user)" class="view-btn">查看详细历史</button>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!loading && processedUserList.length === 0" class="no-data">
      <p v-if="searchUsername">未找到用户 "{{ searchUsername }}"</p>
      <p v-else>请输入用户名进行搜索（如：tourist、jiangly、Benq等）</p>
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="pagination">
      <button
          @click="prevPage"
          :disabled="currentPage === 1"
      >上一页</button>
      <span>第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</span>
      <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
      >下一页</button>
    </div>

    <!-- 历史记录弹窗 -->
    <div v-if="showHistoryModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <div class="user-header">
            <img :src="selectedUser?.avatar" alt="头像" class="modal-avatar">
            <div>
              <h2>{{ selectedUser?.codeforcesId }} 的Rating历史</h2>
              <p class="current-rating">当前Rating: <span :class="getRatingClass(selectedUser?.rating)">{{ selectedUser?.rating }}</span></p>
            </div>
          </div>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div v-if="ratingHistory.length === 0" class="no-history">
            <p>该用户暂无Rating变化记录</p>
          </div>
          <div v-else>
            <!-- Rating变化图表区域 -->
            <div class="chart-container">
              <h3>Rating变化趋势</h3>
              <!-- {{ sortedRatingHistory }} -->
              <div class="rating-chart">
                <div
                  v-for="(record, index) in sortedRatingHistory"
                  :key="record.id"
                  class="chart-point"
                  :style="getChartPointStyle(record, index)"
                  :title="`${formatDate(record.recordTime)}: ${record.oldRating} → ${record.newRating}`"
                >
                  <div class="point"></div>
                  <div class="rating-label">{{ record.newRating }}</div>
                </div>
              </div>
            </div>

            <!-- 详细记录表格 -->
            <div class="table-container">
              <h3>详细记录</h3>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>对战ID</th>
                  <th>变化前Rating</th>
                  <th>变化后Rating</th>
                  <th>变化值</th>
                  <th>结果</th>
                  <th>时间</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(record, index) in sortedRatingHistory" :key="record.id">
                  <td>{{ index + 1 }}</td>
                  <td>{{ record.battleId }}</td>
                  <td>{{ record.oldRating }}</td>
                  <td>{{ record.newRating }}</td>
                  <td :class="getRatingChangeClass(record.newRating - record.oldRating)">
                    {{ formatRatingChange(record.newRating - record.oldRating) }}
                  </td>
                  <td>
                    <span :class="getResultClass(record.reason)">{{ record.reason }}</span>
                  </td>
                  <td>{{ formatDate(record.recordTime) }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { getUserList, getUserDetailHistory, searchUser } from '@/api/api.js';

export default {
  name: 'UserRatingHistory',
  setup() {
    // 基础状态
    const userList = ref([]);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const searchUsername = ref('');
    const loading = ref(false);
    let searchTimer = null;

    // 历史记录相关状态
    const showHistoryModal = ref(false);
    const selectedUser = ref(null);
    const ratingHistory = ref([]);

    // 处理用户列表数据，合并同一用户的多条记录
    const processedUserList = computed(() => {
      const userMap = new Map();

      userList.value.forEach(record => {
        const userId = record.user.id;
        if (!userMap.has(userId)) {
          userMap.set(userId, {
            ...record.user,
            allHistories: [],
            totalHistories: 0
          });
        }

        const user = userMap.get(userId);
        if (record.ratingHistories && record.ratingHistories.length > 0) {
          user.allHistories.push(...record.ratingHistories);
          user.totalHistories = user.allHistories.length;
        }
      });

      return Array.from(userMap.values());
    });

    // 排序后的历史记录（按时间排序）
    const sortedRatingHistory = computed(() => {
      return [...ratingHistory.value].sort((a, b) => new Date(a.recordTime) - new Date(b.recordTime));
    });

    // 获取用户列表
    const fetchUsers = async () => {
      loading.value = true;
      try {
        // 如果没有搜索条件，获取用户列表
        if (!searchUsername.value.trim()) {
          const params = {
            username: '',
            page: currentPage.value,
            size: 10
          }
          const response = await getUserList(params);

          if (response.status && response.data) {
            userList.value = response.data.records;
            totalPages.value = response.data.pages;
            console.log('用户列表数据:', response.data.records);
          }
        } else {
          // 有搜索条件时的正常搜索
          const params = {
            username: searchUsername.value,
            page: currentPage.value,
            size: 10
          }
          const response = await searchUser(params);

          if (response.status) {
            userList.value = response.data.records || [];
            totalPages.value = response.data.pages || 1;
            console.log('搜索结果:', response.data);
          } else {
            console.error('API返回错误:', response.message);
            userList.value = [];
          }
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        // API调用失败时显示空列表
        userList.value = [];
        totalPages.value = 1;
      } finally {
        loading.value = false;
      }
    };

    // 搜索用户（防抖处理）
    const searchUsers = () => {
      if (searchTimer) {
        clearTimeout(searchTimer);
      }
      searchTimer = setTimeout(() => {
        currentPage.value = 1;
        fetchUsers();
      }, 500);
    };

    // 清空搜索
    const clearSearch = () => {
      searchUsername.value = '';
      currentPage.value = 1;
      fetchUsers();
    };

    // 显示历史记录
    const showRatingHistory = (user) => {
      selectedUser.value = user;
      ratingHistory.value = user.allHistories || [];
      showHistoryModal.value = true;
    };

    // 关闭弹窗
    const closeModal = () => {
      showHistoryModal.value = false;
      selectedUser.value = null;
      ratingHistory.value = [];
    };

    // 分页方法
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        fetchUsers();
      }
    };

    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        fetchUsers();
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    // 格式化Rating变化
    const formatRatingChange = (change) => {
      return change > 0 ? `+${change}` : `${change}`;
    };

    // 获取Rating等级样式
    const getRatingClass = (rating) => {
      if (rating >= 3000) return 'rating-red';
      if (rating >= 2400) return 'rating-orange';
      if (rating >= 2100) return 'rating-purple';
      if (rating >= 1900) return 'rating-blue';
      if (rating >= 1600) return 'rating-cyan';
      if (rating >= 1400) return 'rating-green';
      if (rating >= 1200) return 'rating-gray';
      return 'rating-brown';
    };

    // 获取Rating变化样式
    const getRatingChangeClass = (change) => {
      if (change > 0) return 'positive-change';
      if (change < 0) return 'negative-change';
      return 'no-change';
    };

    // 获取结果样式
    const getResultClass = (reason) => {
      return reason.includes('胜利') ? 'result-win' : 'result-lose';
    };

    // 获取图表点位置样式
    const getChartPointStyle = (record, index) => {
      const histories = sortedRatingHistory.value;
      const maxRating = Math.max(...histories.map(h => Math.max(h.oldRating, h.newRating)));
      const minRating = Math.min(...histories.map(h => Math.min(h.oldRating, h.newRating)));
      const ratingRange = maxRating - minRating || 1;

      const leftPercent = (index / (histories.length - 1 || 1)) * 100;
      const bottomPercent = ((record.newRating - minRating) / ratingRange) * 80 + 10;

      return {
        left: `${leftPercent}%`,
        bottom: `${bottomPercent}%`
      };
    };

    // 初始化加载数据
    onMounted(() => {
      // 页面加载时显示默认用户数据
      fetchUsers();
    });

    return {
      // 数据
      userList,
      processedUserList,
      currentPage,
      totalPages,
      searchUsername,
      loading,
      showHistoryModal,
      selectedUser,
      ratingHistory,
      sortedRatingHistory,

      // 方法
      searchUsers,
      clearSearch,
      showRatingHistory,
      closeModal,
      nextPage,
      prevPage,
      formatDate,
      formatRatingChange,
      getRatingClass,
      getRatingChangeClass,
      getResultClass,
      getChartPointStyle
    };
  }
};
</script>

<style scoped>
.user-rating-history {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  min-height: 100vh;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 2.5em;
  font-weight: 300;
}

/* 搜索框样式 */
.search-box {
  margin-bottom: 30px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.search-box input {
  padding: 12px 16px;
  width: 400px;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  border-color: #3498db;
}

.clear-btn {
  padding: 12px 20px;
  background-color: #95a5a6;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.clear-btn:hover {
  background-color: #7f8c8d;
}

/* 当前搜索状态 */
.current-search {
  text-align: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 2px solid #27ae60;
  border-radius: 15px;
  margin-bottom: 20px;
  color: #27ae60;
  font-size: 16px;
}

.current-search strong {
  color: #2c3e50;
  font-weight: 600;
}

/* 加载和无数据状态 */
.loading, .no-data {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
  font-size: 18px;
}

/* 用户列表样式 */
.user-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.user-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e1e8ed;
}

.user-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin-right: 20px;
  object-fit: cover;
  border: 3px solid #e1e8ed;
}

.info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.3em;
  font-weight: 600;
}

.info p {
  margin: 5px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.rating {
  font-weight: 600 !important;
}

.view-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  align-self: flex-end;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  transform: translateY(-2px);
}

/* Rating等级颜色 */
.rating-red { color: #e74c3c; font-weight: bold; }
.rating-orange { color: #f39c12; font-weight: bold; }
.rating-purple { color: #9b59b6; font-weight: bold; }
.rating-blue { color: #3498db; font-weight: bold; }
.rating-cyan { color: #1abc9c; font-weight: bold; }
.rating-green { color: #27ae60; font-weight: bold; }
.rating-gray { color: #95a5a6; font-weight: bold; }
.rating-brown { color: #8b4513; font-weight: bold; }

/* 分页样式 */
.pagination {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.pagination button {
  padding: 12px 24px;
  background-color: #ffffff;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.pagination button:hover:not(:disabled) {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  color: #7f8c8d;
  font-weight: 600;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background-color: white;
  border-radius: 20px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 25px 30px;
  border-bottom: 2px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.modal-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid #e1e8ed;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5em;
  color: #2c3e50;
  font-weight: 600;
}

.current-rating {
  margin: 5px 0 0 0;
  font-size: 14px;
  color: #7f8c8d;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #95a5a6;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: #e74c3c;
  color: white;
}

.modal-body {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.no-history {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
  font-size: 18px;
}

/* 图表样式 */
.chart-container {
  margin-bottom: 40px;
}

.chart-container h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3em;
  font-weight: 600;
}

.rating-chart {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2px solid #e1e8ed;
  border-radius: 15px;
  margin-bottom: 20px;
}

.chart-point {
  position: absolute;
  transform: translate(-50%, 50%);
}

.point {
  width: 8px;
  height: 8px;
  background-color: #3498db;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

.rating-label {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
  background-color: white;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

/* 表格样式 */
.table-container h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3em;
  font-weight: 600;
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

th, td {
  padding: 15px 12px;
  text-align: left;
  border-bottom: 1px solid #e1e8ed;
}

th {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

td {
  font-size: 14px;
  color: #2c3e50;
}

tr:hover {
  background-color: #f8f9fa;
}

/* Rating变化样式 */
.positive-change {
  color: #27ae60;
  font-weight: bold;
}

.negative-change {
  color: #e74c3c;
  font-weight: bold;
}

.no-change {
  color: #95a5a6;
  font-weight: bold;
}

/* 结果样式 */
.result-win {
  color: #27ae60;
  background-color: #d5f4e6;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.result-lose {
  color: #e74c3c;
  background-color: #fdeaea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-rating-history {
    padding: 15px;
  }

  .search-box {
    flex-direction: column;
    gap: 15px;
  }

  .search-box input {
    width: 100%;
    max-width: 300px;
  }

  .user-list {
    grid-template-columns: 1fr;
  }

  .modal {
    width: 95%;
    margin: 20px;
  }

  .modal-header {
    padding: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  table {
    font-size: 12px;
  }

  th, td {
    padding: 10px 8px;
  }
}
</style>