package com.xju.codeduel.web.controller;

import com.xju.codeduel.model.dto.MessageWithUserDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IChatMessagesService;
import com.xju.codeduel.model.domain.ChatMessages;

import java.time.LocalDateTime;
import java.util.List;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-20
 * @version v1.0
 */
@RestController
@RequestMapping("/api/chatMessages")
public class ChatMessagesController {

    private final Logger logger = LoggerFactory.getLogger( ChatMessagesController.class );

    @Autowired
    private IChatMessagesService chatMessagesService;


    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<ChatMessages> getById(@PathVariable("id") Long id)throws Exception {
        ChatMessages chatMessages = chatMessagesService.getById(id);
        return JsonResponse.success(chatMessages);
    }

    /**
     * 获取所有消息及关联用户信息，按时间排序
     * @param codeforcesId 可选参数，筛选特定用户的消息
     * @return 消息及用户信息列表
     */
    @GetMapping("/getMessagesWithUsers")
    @ApiOperation(value = "获取所有消息及关联用户信息", notes = "可传入codeforcesId参数筛选特定用户的消息")
    public JsonResponse<List<MessageWithUserDTO>> getMessagesWithUsers(
            @RequestParam(value = "codeforcesId", required = false) String codeforcesId) {
        List<MessageWithUserDTO> messages = chatMessagesService.getMessagesWithUsers(codeforcesId);
        return JsonResponse.success(messages);
    }

    /**
     * 新增消息
     * @param chatMessages 消息对象（包含 userId、content 等）
     * @return 操作结果
     */
    @PostMapping("/addMessage")
    @ApiOperation(value = "新增消息", notes = "传入消息对象，包含用户ID和消息内容")
    public JsonResponse<Boolean> addMessage(@RequestBody ChatMessages chatMessages) {
        // 设置默认值（如未提供 sentTime 或 isDeleted）
        if (chatMessages.getSentTime() == null) {
            chatMessages.setSentTime(LocalDateTime.now());
        }
        if (chatMessages.getIsDeleted() == null) {
            chatMessages.setIsDeleted(0); // 0 表示未删除
        }

        boolean success = chatMessagesService.save(chatMessages);
        return success ? JsonResponse.success(true) : JsonResponse.failure("添加消息失败");
    }

    /**
     * 删除聊天消息
     * @param messageId 消息ID
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 删除结果
     */
    @DeleteMapping("/{messageId}")
    @ApiOperation(value = "删除聊天消息", notes = "管理员可以删除任意消息，普通用户只能删除自己的消息")
    public JsonResponse<Boolean> deleteMessage(
            @PathVariable("messageId") Long messageId,
            @RequestParam("userId") Long userId,
            @RequestParam(value = "isAdmin", defaultValue = "false") Boolean isAdmin) {
        try {
            logger.info("删除聊天消息请求，消息ID: {}, 用户ID: {}, 是否管理员: {}", messageId, userId, isAdmin);
            // 参数校验
            if (messageId == null) {
                return JsonResponse.failure("消息ID不能为空");
            }
            if (userId == null) {
                return JsonResponse.failure("用户ID不能为空");
            }
            // 查询消息是否存在
            ChatMessages message = chatMessagesService.getById(messageId);
            if (message == null) {
                return JsonResponse.failure("消息不存在");
            }
            // 权限检查：管理员可以删除任意消息，普通用户只能删除自己的消息
            if (!isAdmin && !message.getUserId().equals(userId)) {
                return JsonResponse.failure("无权限删除此消息");
            }

            // 逻辑删除消息（设置is_deleted=1）
            message.setIsDeleted(1);
            boolean success = chatMessagesService.updateById(message);
            if (success) {
                logger.info("聊天消息删除成功，消息ID: {}", messageId);
                return JsonResponse.success(true);
            } else {
                logger.error("聊天消息删除失败，消息ID: {}", messageId);
                return JsonResponse.failure("删除消息失败");
            }
        } catch (Exception e) {
            logger.error("删除聊天消息失败，消息ID: {}", messageId, e);
            return JsonResponse.failure("删除消息失败: " + e.getMessage());
        }
    }

}

