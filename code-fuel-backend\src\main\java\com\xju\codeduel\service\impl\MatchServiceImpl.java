package com.xju.codeduel.service.impl;

import com.xju.codeduel.mapper.UsersMapper;
import com.xju.codeduel.model.dto.MatchRequestDTO;
import com.xju.codeduel.model.dto.RoomCreateDTO;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.model.dto.RoomJoinDTO;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.service.IMatchService;
import com.xju.codeduel.service.IRoomService;
import com.xju.codeduel.service.UserRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 匹配服务实现类
 *
 * 负责处理用户匹配逻辑，包括：
 * 1. 用户匹配请求的处理
 * 2. 匹配算法的实现（基于 Rating 和等待时间）
 * 3. 匹配成功后的房间创建
 * 4. 匹配状态的查询和管理
 * 5. 匹配队列的维护和清理
 *
 * <AUTHOR>
 * @since 2025-07-24
 */

/**
 * 匹配服务实现类
 * 
 * 功能说明：
 * 1. 管理匹配队列（内存中存储）
 * 2. 实现基于Rating和难度偏好的匹配算法
 * 3. 自动创建匹配房间
 * 4. 处理匹配超时和清理
 * 
 * 匹配算法：
 * - 优先匹配Rating相近的用户（差距200分以内）
 * - 考虑难度偏好重叠
 * - 等待时间越长，匹配条件越宽松
 * - 最长等待时间5分钟，超时自动取消
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class MatchServiceImpl implements IMatchService {

    private static final Logger logger = LoggerFactory.getLogger(MatchServiceImpl.class);

    // 匹配队列（内存存储）
    private final Map<Long, MatchRequestDTO> matchQueue = new ConcurrentHashMap<>();

    // 匹配锁，防止并发匹配导致的状态不一致
    private final Object matchLock = new Object();

    // 最大等待时间（秒）
    private static final long MAX_WAITING_TIME = 300; // 5分钟

    @Autowired
    private UsersMapper usersMapper;

    @Autowired
    private IRoomService roomService;

    @Autowired
    private UserRoomService userRoomService;

    @Override
    public Map<String, Object> startMatch(Long userId, Integer minDifficulty, Integer maxDifficulty) {
        logger.info("🎯 开始匹配，用户: {}, 难度范围: {}-{}", userId, minDifficulty, maxDifficulty);

        try {
            // 1. 验证用户状态
            if (matchQueue.containsKey(userId)) {
                throw new RuntimeException("用户已在匹配队列中");
            }

            // 检查用户是否在房间中
            if (userRoomService.isUserInRoom(userId)) {
                throw new RuntimeException("用户当前在房间中，无法开始匹配");
            }

            // 2. 获取用户信息
            Users user = usersMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 3. 创建匹配请求
            MatchRequestDTO matchRequest = new MatchRequestDTO(
                userId, user.getCodeforcesId(), user.getRating(),
                minDifficulty, maxDifficulty
            );

            // 4. 尝试寻找合适的对手
            MatchRequestDTO opponent = findOpponent(matchRequest);

            if (opponent != null) {
                // 找到对手，创建房间并开始对战
                return createMatchRoom(matchRequest, opponent);
            } else {
                // 没有找到对手，加入等待队列
                matchQueue.put(userId, matchRequest);
                logger.info("🕐 用户 {} 加入匹配队列等待", user.getCodeforcesId());

                Map<String, Object> result = new HashMap<>();
                result.put("status", "WAITING");
                result.put("message", "正在寻找对手，请稍候...");
                result.put("waitingTime", 0);
                return result;
            }

        } catch (Exception e) {
            logger.error("❌ 开始匹配失败: {}", e.getMessage(), e);
            throw new RuntimeException("开始匹配失败: " + e.getMessage());
        }
    }

    @Override
    public boolean cancelMatch(Long userId) {
        logger.info("🚫 取消匹配，用户: {}", userId);

        MatchRequestDTO removed = matchQueue.remove(userId);
        if (removed != null) {
            logger.info("✅ 用户 {} 已从匹配队列中移除", removed.getCodeforcesId());
            return true;
        } else {
            logger.warn("⚠️ 用户 {} 不在匹配队列中", userId);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMatchStatus(Long userId) {
        logger.info("📊 查询匹配状态，用户: {}", userId);

        // 使用同步锁防止并发匹配，包含整个状态查询逻辑
        synchronized (matchLock) {
            MatchRequestDTO matchRequest = matchQueue.get(userId);
            Map<String, Object> status = new HashMap<>();

            if (matchRequest == null) {
                status.put("status", "NOT_MATCHING");
                status.put("message", "用户不在匹配队列中");
                return status;
            }

            logger.info("🔍 用户 {} 当前状态: {}, 房间: {}",
                matchRequest.getCodeforcesId(), matchRequest.getStatus(), matchRequest.getMatchedRoomCode());

            // 更新等待时间
            matchRequest.updateWaitingTime();

            // 检查是否超时（5分钟）
            if (matchRequest.getWaitingTime() > 300) {
                matchRequest.setStatus("TIMEOUT");
                matchQueue.remove(userId);
                status.put("status", "TIMEOUT");
                status.put("message", "匹配超时，请重新开始匹配");
                logger.info("⏰ 用户 {} 匹配超时", matchRequest.getCodeforcesId());
                return status;
            }

            // 如果已经匹配成功，直接返回
            if ("MATCHED".equals(matchRequest.getStatus())) {
                status.put("status", "MATCHED");
                status.put("roomCode", matchRequest.getMatchedRoomCode());
                status.put("message", "匹配成功！5秒后进入房间...");

                // 查找对手信息
                String opponentName = "未知对手";
                for (MatchRequestDTO otherRequest : matchQueue.values()) {
                    if (otherRequest.getMatchedRoomCode() != null &&
                        otherRequest.getMatchedRoomCode().equals(matchRequest.getMatchedRoomCode()) &&
                        !otherRequest.getUserId().equals(matchRequest.getUserId())) {
                        opponentName = otherRequest.getCodeforcesId();
                        break;
                    }
                }
                status.put("opponent", opponentName);

                // 获取房间详细信息
                try {
                    RoomInfoDTO roomInfo = roomService.getRoomInfo(matchRequest.getMatchedRoomCode());
                    if (roomInfo != null) {
                        status.put("roomInfo", roomInfo);
                    }
                } catch (Exception e) {
                    logger.warn("⚠️ 获取房间信息失败: {}", e.getMessage());
                }

                // 检查是否应该清理匹配队列（匹配成功30秒后）
                if (matchRequest.getMatchedTime() != null &&
                    System.currentTimeMillis() - matchRequest.getMatchedTime() > 30000) {
                    logger.info("🧹 清理已匹配用户 {} 的队列记录", matchRequest.getCodeforcesId());
                    matchQueue.remove(userId);
                }

                logger.info("✅ 返回匹配成功状态，用户: {}, 对手: {}", matchRequest.getCodeforcesId(), opponentName);
                return status;
            }

            // 如果还在等待中，尝试寻找对手
            if ("WAITING".equals(matchRequest.getStatus())) {
                MatchRequestDTO opponent = findOpponent(matchRequest);

                if (opponent != null) {
                    try {
                        // 找到对手，创建房间
                        Map<String, Object> matchResult = createMatchRoom(matchRequest, opponent);

                        // 更新两个用户的状态为已匹配
                        matchRequest.setStatus("MATCHED");
                        matchRequest.setMatchedRoomCode((Long) matchResult.get("roomCode"));
                        logger.info("🔄 更新用户 {} 状态为 MATCHED，房间: {}",
                            matchRequest.getCodeforcesId(), matchResult.get("roomCode"));

                        opponent.setStatus("MATCHED");
                        opponent.setMatchedRoomCode((Long) matchResult.get("roomCode"));
                        logger.info("🔄 更新对手 {} 状态为 MATCHED，房间: {}",
                            opponent.getCodeforcesId(), matchResult.get("roomCode"));

                        // 设置匹配成功的时间戳，用于后续清理
                        long currentTime = System.currentTimeMillis();
                        matchRequest.setMatchedTime(currentTime);
                        opponent.setMatchedTime(currentTime);

                        status.put("status", "MATCHED");
                        status.put("roomCode", matchResult.get("roomCode"));
                        status.put("roomInfo", matchResult.get("roomInfo"));
                        status.put("opponent", opponent.getCodeforcesId());
                        status.put("message", "匹配成功！找到对手 " + opponent.getCodeforcesId());

                        logger.info("✅ 匹配成功，用户: {} vs {}, 房间: {}",
                            matchRequest.getCodeforcesId(), opponent.getCodeforcesId(), matchResult.get("roomCode"));

                        return status;

                    } catch (Exception e) {
                        logger.error("❌ 创建匹配房间失败: {}", e.getMessage(), e);
                        // 继续等待
                    }
                }
            }

            status.put("status", matchRequest.getStatus());
            status.put("waitingTime", matchRequest.getWaitingTime());
            status.put("message", "正在寻找对手，已等待 " + matchRequest.getWaitingTime() + " 秒");

            return status;
        }
    }

    @Override
    public void handleMatchTimeout() {
        logger.debug("🧹 检查匹配超时...");
        
        List<Long> timeoutUsers = new ArrayList<>();
        
        for (Map.Entry<Long, MatchRequestDTO> entry : matchQueue.entrySet()) {
            MatchRequestDTO request = entry.getValue();
            request.updateWaitingTime();
            
            if (request.getWaitingTime() > MAX_WAITING_TIME) {
                timeoutUsers.add(entry.getKey());
            }
        }
        
        for (Long userId : timeoutUsers) {
            MatchRequestDTO removed = matchQueue.remove(userId);
            if (removed != null) {
                logger.info("⏰ 匹配超时，移除用户: {}", removed.getCodeforcesId());
            }
        }
        
        if (!timeoutUsers.isEmpty()) {
            logger.info("🧹 清理了 {} 个超时的匹配请求", timeoutUsers.size());
        }
    }

    @Override
    public void cleanupMatchQueue() {
        logger.debug("🧹 清理匹配队列...");

        // 这里可以添加更多的清理逻辑，比如检查用户是否在线等
        // 目前主要依赖超时处理
        handleMatchTimeout();
    }

    /**
     * 寻找合适的对手
     */
    private MatchRequestDTO findOpponent(MatchRequestDTO request) {
        logger.info("🔍 为用户 {} (Rating: {}) 寻找对手，当前队列大小: {}",
                   request.getCodeforcesId(), request.getUserRating(), matchQueue.size());

        MatchRequestDTO bestOpponent = null;
        int bestScore = Integer.MAX_VALUE;

        for (MatchRequestDTO candidate : matchQueue.values()) {
            // 跳过自己
            if (candidate.getUserId().equals(request.getUserId())) {
                logger.debug("⏭️ 跳过自己: {}", candidate.getCodeforcesId());
                continue;
            }

            // 跳过已匹配的用户
            if ("MATCHED".equals(candidate.getStatus())) {
                logger.debug("⏭️ 跳过已匹配用户: {}", candidate.getCodeforcesId());
                continue;
            }

            // 更新候选者的等待时间
            candidate.updateWaitingTime();

            logger.info("🔍 检查候选者: {} (Rating: {}), 等待时间: {}秒, 状态: {}",
                        candidate.getCodeforcesId(), candidate.getUserRating(),
                        candidate.getWaitingTime(), candidate.getStatus());

            // 检查兼容性
            if (request.isCompatibleWith(candidate)) {
                // 计算匹配分数（分数越低越好）
                int score = calculateMatchScore(request, candidate);

                logger.info("✅ 兼容的候选者: {}, 匹配分数: {}", candidate.getCodeforcesId(), score);

                if (score < bestScore) {
                    bestScore = score;
                    bestOpponent = candidate;
                }
            } else {
                int ratingDiff = Math.abs(request.getUserRating() - candidate.getUserRating());
                logger.info("❌ 不兼容的候选者: {}, Rating差距: {}", candidate.getCodeforcesId(), ratingDiff);
            }
        }

        if (bestOpponent != null) {
            logger.info("✅ 找到合适对手: {} vs {}, 匹配分数: {}",
                       request.getCodeforcesId(), bestOpponent.getCodeforcesId(), bestScore);
        } else {
            logger.info("❌ 未找到合适对手，用户: {}", request.getCodeforcesId());
        }

        return bestOpponent;
    }

    /**
     * 计算匹配分数（分数越低越好）
     */
    private int calculateMatchScore(MatchRequestDTO user1, MatchRequestDTO user2) {
        // Rating差距权重
        int ratingDiff = Math.abs(user1.getUserRating() - user2.getUserRating());

        // 等待时间权重（等待越久，优先级越高）
        long totalWaitingTime = user1.getWaitingTime() + user2.getWaitingTime();

        // 难度偏好重叠度
        int overlapMin = Math.max(user1.getMinDifficulty(), user2.getMinDifficulty());
        int overlapMax = Math.min(user1.getMaxDifficulty(), user2.getMaxDifficulty());
        int overlapSize = Math.max(0, overlapMax - overlapMin);

        // 计算综合分数
        int score = ratingDiff * 2 - (int)(totalWaitingTime / 10) + (2000 - overlapSize);

        return Math.max(0, score);
    }

    /**
     * 创建匹配房间
     */
    private Map<String, Object> createMatchRoom(MatchRequestDTO user1, MatchRequestDTO user2) {
        logger.info("🏠 创建匹配房间: {} vs {}", user1.getCodeforcesId(), user2.getCodeforcesId());

        try {
            // 先设置用户状态为匹配成功，但保留在队列中让前端能查询到状态
            long currentTime = System.currentTimeMillis();
            user1.setStatus("MATCHED");
            user1.setMatchedRoomCode(null); // 房间还未创建，先设为null
            user1.setMatchedTime(currentTime);
            user2.setStatus("MATCHED");
            user2.setMatchedRoomCode(null);
            user2.setMatchedTime(currentTime);

            logger.info("✅ 用户 {} 和 {} 状态已设置为MATCHED", user1.getCodeforcesId(), user2.getCodeforcesId());

            // 计算房间难度范围（取两人偏好的交集）
            int roomMinDifficulty = Math.max(user1.getMinDifficulty(), user2.getMinDifficulty());
            int roomMaxDifficulty = Math.min(user1.getMaxDifficulty(), user2.getMaxDifficulty());

            // 创建房间（使用user1作为房主）
            RoomCreateDTO createDTO = new RoomCreateDTO();
            createDTO.setCreatorId(user1.getUserId());
            createDTO.setMinDifficulty(roomMinDifficulty);
            createDTO.setMaxDifficulty(roomMaxDifficulty);
            createDTO.setDescription("匹配对战: " + user1.getCodeforcesId() + " vs " + user2.getCodeforcesId());
            createDTO.setExcludedTags(new ArrayList<>()); // 匹配模式不排除标签

            RoomInfoDTO roomInfo = roomService.createRoom(createDTO);

            // 标记为匹配对战并更新缓存
            roomInfo.setIsMatchBattle(true);
            roomService.updateRoomCache(roomInfo.getRoomCode(), roomInfo);

            logger.info("🏷️ 房间 {} 已标记为匹配对战", roomInfo.getRoomCode());

            // user2自动加入房间（只有当user2不是房主时才加入）
            if (!user2.getUserId().equals(user1.getUserId())) {
                RoomJoinDTO joinDTO = new RoomJoinDTO();
                joinDTO.setRoomCode(roomInfo.getRoomCode());
                joinDTO.setUserId(user2.getUserId());

                roomService.joinRoom(joinDTO);
            }

            // 匹配房间创建后立即自动开始对战
            roomService.startBattle(roomInfo.getRoomCode());
            logger.info("🎯 匹配房间 {} 已自动开始对战", roomInfo.getRoomCode());

            logger.info("✅ 匹配房间创建成功，房间码: {}", roomInfo.getRoomCode());

            // 更新用户的房间码信息
            user1.setMatchedRoomCode(roomInfo.getRoomCode());
            user2.setMatchedRoomCode(roomInfo.getRoomCode());

            logger.info("✅ 用户 {} 和 {} 房间码已更新为: {}", user1.getCodeforcesId(), user2.getCodeforcesId(), roomInfo.getRoomCode());

            // 返回匹配成功结果
            Map<String, Object> result = new HashMap<>();
            result.put("status", "MATCHED");
            result.put("message", "匹配成功！正在进入对战...");
            result.put("roomCode", roomInfo.getRoomCode());
            result.put("roomInfo", roomInfo);
            result.put("opponent", user2.getCodeforcesId());

            return result;

        } catch (Exception e) {
            logger.error("❌ 创建匹配房间失败: {}", e.getMessage(), e);

            // 失败时重置用户状态并重新加入队列
            user1.setStatus("WAITING");
            user1.setMatchedRoomCode(null);
            user2.setStatus("WAITING");
            user2.setMatchedRoomCode(null);

            matchQueue.put(user1.getUserId(), user1);
            matchQueue.put(user2.getUserId(), user2);
            logger.info("🔄 房间创建失败，用户 {} 和 {} 状态已重置并重新加入匹配队列", user1.getCodeforcesId(), user2.getCodeforcesId());

            throw new RuntimeException("创建匹配房间失败: " + e.getMessage());
        }
    }

    @Override
    public void forceRemoveFromQueue(Long userId) {
        MatchRequestDTO removed = matchQueue.remove(userId);
        if (removed != null) {
            logger.info("🧹 强制清理用户 {} 的匹配队列记录", removed.getCodeforcesId());
        }
        // 不抛出异常，即使用户不在队列中
    }
}
