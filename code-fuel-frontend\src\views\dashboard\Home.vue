<template>
  <div class="home-container">
    <!-- 系统主页欢迎横幅 -->
    <el-card class="welcome-banner" shadow="hover">
      <div class="banner-content">
        <h1>欢迎来到 CodeDuel</h1>
        <p>在这里挑战自己，与全球程序员一较高下！</p>
        <el-button type="primary" size="large" @click="startBattle">
          <el-icon><Promotion /></el-icon>
          开始对战
        </el-button>
      </div>

    </el-card>

    <!-- 统计数据 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><User /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">注册用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><Trophy /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalBattles }}</div>
              <div class="stat-label">总对战数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><Document /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalProblems }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><ChatDotRound /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalPosts }}</div>
              <div class="stat-label">发帖总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要功能区域 - 三等分布局 -->
    <el-row :gutter="20" class="main-features">
      <!-- 排行榜预览 -->
      <el-col :span="8">
        <el-card class="feature-card" header="排行榜 Top 10">
          <div class="ranking-preview">
            <div v-for="(user, index) in topRanking" :key="user.id" class="ranking-item">
              <div class="rank-number">{{ index + 1 }}</div>
              <el-avatar :size="30" :src="user.avatar || '/default-avatar.png'" />
              <div class="user-info">
                <div class="username">{{ user.codeforcesId }}</div>
                <div class="rating">Rating: {{ user.rating }} ({{ user.totalBattles }}场)</div>
              </div>
            </div>
          </div>
          <div class="view-more">
            <el-button type="text" @click="viewRanking">查看完整排行榜</el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 最近对战记录 -->
      <el-col :span="8">
        <el-card class="feature-card" header="最近对战">
          <div class="battle-history">
            <div v-for="battle in recentBattles" :key="battle.battleRecord.id" class="battle-item">
              <div class="battle-info">
                <div class="battle-title">{{ battle.problem?.title || '未知题目' }}</div>
                <div class="battle-time">{{ formatTime(battle.battleRecord.startTime) }}</div>
                <div class="battle-participants">
                  参与者: {{ battle.participants?.map(p => p.codeforcesId).join(', ') || '无' }}
                </div>
              </div>
              <div class="battle-result">
                <el-tag :type="battle.battleRecord.isRoom ? 'warning' : 'info'">
                  {{ battle.battleRecord.isRoom ? '房间对战' : '匹配对战' }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="view-more">
            <el-button type="text" @click="viewBattleHistory">查看更多记录</el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 最新发帖 -->
      <el-col :span="8">
        <el-card class="feature-card" header="最新发帖">
          <div class="forum-preview">
            <div v-for="post in hotPosts" :key="post.post.id" class="post-item">
              <div class="post-content">
                <div class="post-title">{{ post.post.title }}</div>
                <div class="post-meta">
                  <span>{{ post.user?.codeforcesId || '匿名用户' }}</span>
                  <span>{{ formatTime(post.post.postTime) }}</span>
                  <span>{{ post.post.commentCount || 0 }} 评论</span>
                  <span v-if="post.post.isTop">🔝 置顶</span>
                </div>
              </div>
              <el-button type="text" size="small" @click="viewPost(post.post.id)">查看</el-button>
            </div>
          </div>
          <div class="view-more">
            <el-button type="text" @click="viewForum">进入讨论区</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  Trophy,
  Document,
  ChatDotRound,
  Promotion
} from '@element-plus/icons-vue'
import { getHomeStats, getUserList, getRecentBattleRecords, getPagePosts } from '@/api/api'

const router = useRouter()

// 统计数据
const stats = ref({
  totalUsers: 0,
  totalBattles: 0,
  totalProblems: 0,
  totalPosts: 0
})

// 排行榜数据
const topRanking = ref([])

// 最近对战记录
const recentBattles = ref([])

// 热门帖子
const hotPosts = ref([])

// 加载状态
const loading = ref(false)

// 方法
const startBattle = () => {
  router.push('/dashboard/battle')
}

const viewRanking = () => {
  router.push('/dashboard/ranking')
}

const viewBattleHistory = () => {
  router.push('/user/info')
}

const viewForum = () => {
  router.push('/dashboard/forum')
}

const viewPost = (postId) => {
  router.push(`/forum/post/${postId}`)
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await getHomeStats()
    if (response.status) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 加载排行榜数据
const loadTopRanking = async () => {
  try {
    const response = await getUserList({ page: 1, size: 10 })
    if (response.status && response.data) {
      topRanking.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载排行榜失败:', error)
    ElMessage.error('加载排行榜失败')
  }
}

// 加载最近对战记录
const loadRecentBattles = async () => {
  try {
    const response = await getRecentBattleRecords({ page: 1, size: 10 })
    if (response.status && response.data) {
      recentBattles.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载对战记录失败:', error)
    ElMessage.error('加载对战记录失败')
  }
}

// 加载热门帖子
const loadHotPosts = async () => {
  try {
    const response = await getPagePosts({ pageNo: 1, pageSize: 10 })
    if (response.status && response.data) {
      hotPosts.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载帖子失败:', error)
    ElMessage.error('加载帖子失败')
  }
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadTopRanking(),
      loadRecentBattles(),
      loadHotPosts()
    ])
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadAllData()
})
</script>

<style lang="scss" scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;

  .welcome-banner {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .banner-content {
      text-align: center;
      padding: 40px 0;

      h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      p {
        font-size: 1.2em;
        margin-bottom: 30px;
        opacity: 0.9;
      }
    }
  }

  .stats-row {
    margin-bottom: 20px;

    .stat-card {
      .stat-item {
        display: flex;
        align-items: center;

        .stat-icon {
          font-size: 2em;
          color: #409EFF;
          margin-right: 15px;
        }

        .stat-content {
          .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
          }

          .stat-label {
            color: #666;
            font-size: 0.9em;
          }
        }
      }
    }
  }

  .main-features {
    margin-bottom: 20px;
  }

  .feature-card {
    margin-bottom: 20px;
    height: 650px; // 增加高度以更好地显示10条数据

    .el-card__body {
      height: calc(100% - 60px); // 减去header高度
      display: flex;
      flex-direction: column;
      padding: 15px;
    }

    .ranking-preview, .battle-history, .forum-preview {
      flex: 1;
      overflow-y: auto; // 允许滚动
      max-height: 520px; // 增加最大高度

      .ranking-item, .battle-item, .post-item {
        display: flex;
        align-items: center;
        padding: 10px 0; // 适当的padding
        border-bottom: 1px solid #f0f0f0;
        min-height: 50px; // 增加最小高度

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .ranking-preview {
      .ranking-item {
        .rank-number {
          width: 30px;
          text-align: center;
          font-weight: bold;
          color: #409EFF;
          font-size: 0.9em;
        }

        .user-info {
          margin-left: 10px;
          flex: 1;

          .username {
            font-weight: bold;
            font-size: 0.9em;
          }

          .rating {
            color: #666;
            font-size: 0.8em;
          }
        }
      }
    }

    .battle-history {
      .battle-item {
        justify-content: space-between;

        .battle-info {
          flex: 1;

          .battle-title {
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 2px;
          }

          .battle-time {
            color: #666;
            font-size: 0.8em;
          }

          .battle-participants {
            color: #888;
            font-size: 0.75em;
            margin-top: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .battle-result {
          margin-left: 10px;

          .el-tag {
            font-size: 0.75em;
          }
        }
      }
    }

    .forum-preview {
      .post-item {
        justify-content: space-between;

        .post-content {
          flex: 1;

          .post-title {
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .post-meta {
            color: #666;
            font-size: 0.75em;

            span {
              margin-right: 10px;
            }
          }
        }

        .el-button {
          margin-left: 10px;
          font-size: 0.75em;
        }
      }
    }

    .view-more {
      text-align: center;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #f0f0f0;
      flex-shrink: 0; // 防止被压缩
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .home-container {
    .main-features {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .feature-card {
      height: auto;
      min-height: 500px;
    }
  }
}

@media (max-width: 768px) {
  .home-container {
    .main-features {
      .el-col {
        span: 24; // 在小屏幕上堆叠显示
      }
    }

    .feature-card {
      height: auto;
      min-height: 400px;

      .ranking-preview, .battle-history, .forum-preview {
        max-height: 350px;
      }
    }
  }
}
</style>
