package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Posts;
import com.xju.codeduel.mapper.PostsMapper;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.PostWithUserDTO;
import com.xju.codeduel.service.IPostsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 帖子 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class PostsServiceImpl extends ServiceImpl<PostsMapper, Posts> implements IPostsService {
    @Autowired
    private PostsMapper postsMapper;

    @Override
    public List<PostWithUserDTO> getPostsWithUserByTitle(String title) {
        return baseMapper.selectPostsWithUserByTitle(title);
    }

    @Override
    public Page<PostWithUserDTO> pageposts(PageDTO pageDto, String title) {
        Page<PostWithUserDTO> page =new Page<>(pageDto.getPageNo(),pageDto.getPageSize());
        page = postsMapper.pageposts(page,title);
        return page;
    }

    @Override
    public PostWithUserDTO getPostWithUserById(Long id) {
        return postsMapper.getPostWithUserById(id);
    }
}
