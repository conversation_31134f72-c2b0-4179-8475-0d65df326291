import request from "@/utils/request";

//登录
export const usersLogin = users => request.post("/api/users/login", users);

//获取用户
export const getInfo = () => request.get("/api/auth/getUserInfo");

//根据用户名获取用户信息
export const getUserByUsername = username => request.get(`/api/users/profile/${username}`);

//分页查询
export const pageUsers = query => request.get("/api/users/pageList", { params: query });

//修改用户信息
export const updateUser = user => request.put("/api/users/update", user);

//获取帖子列表
export const getPosts = () => request.get("/api/posts");

//获取用户列表（仅基本信息 + 对战记录条数，用于排行榜）
export const getUserList = params => request.get("/api/users/list", { params });

//搜索用户（仅基本信息）
export const searchUser = params => request.get("/api/users/list", { params });

//获取用户详细历史记录（用于详情查看）
export const getUserDetailHistory = username => request.get(`/api/users/rating-history/${username}`);

//获取聊天消息列表
export const getChatMessages = () => request.get("/api/chatMessages/getMessagesWithUsers");

//发送聊天消息
export const sendChatMessage = (message) => request.post("/api/chatMessages/addMessage", message);

//删除聊天消息
export const deleteChatMessage = (messageId, params) => request.delete(`/api/chatMessages/${messageId}`, { params });

//获取帖子列表（分页）
export const getPostsList = (params) => request.get("/api/posts/pagePosts", { params });

//创建新帖子
export const createPost = (post) => request.post("/api/posts/create", post);

//获取单个帖子详情
export const getPostById = (id) => request.get(`/api/posts/${id}`);

//更新帖子
export const updatePost = (postId, post, params) => request.put(`/api/posts/${postId}`, post, { params });

//删除帖子
export const deletePost = (postId, params) => request.delete(`/api/posts/${postId}`, { params });

//切换帖子置顶状态
export const togglePinPost = (postId, params) => request.put(`/api/posts/${postId}/pin`, {}, { params });

// Codeforces验证注册相关API
//生成验证码
export const generateVerificationCode = () => request.post("/api/users/generate-verification-code");

//验证Codeforces用户身份
export const verifyCodeforcesUser = (data) => request.post("/api/users/verify-codeforces", data);

//获取帖子评论列表（分页）
export const getCommentsByPostId = (postId, params) => request.get(`/api/comments/post/${postId}`, { params });

//获取帖子所有评论（不分页）
export const getAllCommentsByPostId = (postId) => request.get(`/api/comments/post/${postId}/all`);

//发布评论
export const createComment = (comment) => request.post("/api/comments/create", comment);

//删除评论
export const deleteComment = (commentId, params) => request.delete(`/api/comments/${commentId}`, { params });

//用户注册
export const userRegister = (data) => request.post("/api/users/register", data);


//测试注册（跳过验证）
export const testRegister = (data) => request.post("/api/users/register", data);

//分页获取帖子列表
export const getPagePosts = params => request.get("/api/posts/pagePosts", { params });

//获取首页统计数据
export const getHomeStats = () => request.get("/api/users/home-stats");

//获取最近对战记录
export const getRecentBattleRecords = params => request.get("/api/battleRecords/recent", { params });

// ==================== 房间管理相关API ====================

//创建房间
export const createRoom = data => request.post("/api/room/create", data);

//加入房间
export const joinRoom = data => request.post("/api/room/join", data);

//离开房间
export const leaveRoom = data => request.post("/api/room/leave", data);

//获取房间信息
export const getRoomInfo = roomCode => request.get(`/api/room/${roomCode}`);

//开始对战
export const startBattle = roomCode => request.post(`/api/room/${roomCode}/start`);

//获取活跃房间列表
export const getActiveRooms = () => request.get("/api/room/active");

//生成房间码
export const generateRoomCode = () => request.get("/api/room/generate-code");

// ==================== 标签相关API ====================

//获取所有标签
export const getAllTags = () => request.get("/api/tags/all");

// ==================== 对战相关API ====================

//检查提交状态
export const checkSubmissionStatus = data => request.post("/api/battle/check-submission", data);

//结束对战
export const finishBattle = data => request.post("/api/room/finish-battle", data);

// ==================== 匹配相关API ====================

//开始匹配
export const startMatch = data => request.post("/api/match/start", data);

//取消匹配
export const cancelMatch = data => request.post("/api/match/cancel", data);

//获取匹配状态
export const getMatchStatus = userId => request.get(`/api/match/status/${userId}`);

// ==================== 用户房间状态相关API ====================

//清理用户房间状态
export const clearUserRoomStatus = data => request.post("/api/user/clear-status", data);

// ==================== 数据管理相关API ====================

//从Codeforces更新题目数据
export const updateProblemsFromCodeforces = data => request.post("/api/admin/update-problems", data);

//从Codeforces更新标签数据
export const updateTagsFromCodeforces = data => request.post("/api/admin/update-tags", data);

//获取数据更新状态
export const getDataUpdateStatus = () => request.get("/api/admin/data-status");

//分页查询题目列表
export const getProblemsPage = params => request.get("/api/admin/problems", { params });

//分页查询标签列表
export const getTagsPage = params => request.get("/api/admin/tags", { params });

// ==================== 用户房间状态相关API ====================

//获取用户当前房间状态
export const getCurrentRoomStatus = () => request.get("/api/user/current-room");

//重新加入房间
export const rejoinRoom = roomCode => request.post("/api/user/rejoin-room", { roomCode });

//离开当前房间
export const leaveCurrentRoom = () => request.post("/api/user/leave-room");