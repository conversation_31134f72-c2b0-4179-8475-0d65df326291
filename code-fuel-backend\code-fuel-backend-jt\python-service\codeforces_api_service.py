#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Codeforces API服务

这是一个独立的Python微服务，专门负责与Codeforces官方API的交互。
设计目的：
1. 解决Java HTTP客户端访问Codeforces API不稳定的问题
2. 提供稳定可靠的网络请求处理
3. 将外部API调用从主业务系统中分离
4. 提供统一的Codeforces数据访问接口

技术栈：
- Flask: 轻量级Web框架，提供RESTful API
- requests: 强大的HTTP库，处理网络请求
- flask-cors: 处理跨域请求

服务架构：
前端 ←→ Java后端 ←→ Python服务 ←→ Codeforces API
"""

import requests
import json
import time
from flask import Flask, jsonify, request
from flask_cors import CORS

# 创建Flask应用实例
app = Flask(__name__)
# 启用CORS支持，允许Java后端跨域访问
CORS(app)

# ==================== 配置常量 ====================
# Codeforces官方API基础URL
CODEFORCES_API_BASE = "https://codeforces.com/api"
# HTTP请求超时时间（秒）
REQUEST_TIMEOUT = 30
# 最大重试次数（网络不稳定时重试）
MAX_RETRIES = 3
# 重试间隔时间（秒）
RETRY_DELAY = 2

def get_codeforces_problemset(tags=None):
    """
    获取Codeforces题目集

    Args:
        tags (list): 标签过滤列表

    Returns:
        dict: 题目集数据，包含problems和problemStatistics
        None: 如果获取失败返回None
    """
    url = f"{CODEFORCES_API_BASE}/problemset.problems"
    params = {}

    if tags:
        params['tags'] = ';'.join(tags)

    for attempt in range(MAX_RETRIES):
        try:
            print(f"[尝试 {attempt + 1}/{MAX_RETRIES}] 获取题目集，参数: {params}")

            response = requests.get(
                url,
                params=params,
                timeout=REQUEST_TIMEOUT,
                headers={
                    'User-Agent': 'CodeDuel-Python-Service/1.0'
                }
            )

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == 'OK' and data.get('result'):
                    result = data['result']
                    problems = result.get('problems', [])
                    print(f"✅ 成功获取 {len(problems)} 道题目")
                    return result
                else:
                    print(f"❌ Codeforces API返回错误: {data.get('comment', '未知错误')}")
                    return None
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")

        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时 (尝试 {attempt + 1}/{MAX_RETRIES})")
        except requests.exceptions.RequestException as e:
            print(f"💥 网络请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"📄 JSON解析失败: {e}")
        except Exception as e:
            print(f"🔥 未知异常: {e}")

        if attempt < MAX_RETRIES - 1:
            print(f"⏳ 等待 {RETRY_DELAY} 秒后重试...")
            time.sleep(RETRY_DELAY)

    print(f"❌ 获取题目集失败，已重试 {MAX_RETRIES} 次")
    return None

def get_user_submissions(handle, from_time=None, count=10000):
    """
    获取用户提交记录

    Args:
        handle (str): 用户名
        from_time (int): 起始时间戳
        count (int): 获取数量

    Returns:
        list: 提交记录列表
        None: 如果获取失败返回None
    """
    url = f"{CODEFORCES_API_BASE}/user.status"
    params = {
        'handle': handle,
        'count': count
    }

    if from_time:
        params['from'] = from_time

    for attempt in range(MAX_RETRIES):
        try:
            print(f"[尝试 {attempt + 1}/{MAX_RETRIES}] 获取用户 {handle} 的提交记录")

            response = requests.get(
                url,
                params=params,
                timeout=REQUEST_TIMEOUT,
                headers={
                    'User-Agent': 'CodeDuel-Python-Service/1.0'
                }
            )

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == 'OK' and data.get('result'):
                    submissions = data['result']
                    print(f"✅ 成功获取用户 {handle} 的 {len(submissions)} 条提交记录")
                    return submissions
                else:
                    print(f"❌ Codeforces API返回错误: {data.get('comment', '未知错误')}")
                    return None
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")

        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时 (尝试 {attempt + 1}/{MAX_RETRIES})")
        except requests.exceptions.RequestException as e:
            print(f"💥 网络请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"📄 JSON解析失败: {e}")
        except Exception as e:
            print(f"🔥 未知异常: {e}")

        if attempt < MAX_RETRIES - 1:
            print(f"⏳ 等待 {RETRY_DELAY} 秒后重试...")
            time.sleep(RETRY_DELAY)

    print(f"❌ 获取用户 {handle} 提交记录失败，已重试 {MAX_RETRIES} 次")
    return None

def get_codeforces_user_info(handle):
    """
    获取Codeforces用户完整信息
    
    这是核心函数，负责从Codeforces官方API获取用户的完整信息。
    使用了重试机制来提高网络请求的成功率。
    
    技术实现：
    1. 构建Codeforces API请求URL
    2. 发送HTTP GET请求
    3. 解析JSON响应
    4. 提取用户信息
    5. 错误处理和重试机制
    
    Args:
        handle (str): Codeforces用户名（句柄）
        
    Returns:
        dict: 用户完整信息字典，包含以下字段：
            - handle: 用户名
            - firstName: 名字（用于身份验证）
            - avatar: 头像URL
            - rating: 当前Rating
            - country: 国家
            - organization: 组织
            等等...
        None: 如果获取失败返回None
    """
    url = f"{CODEFORCES_API_BASE}/user.info?handles={handle}"
    
    for attempt in range(MAX_RETRIES):
        try:
            print(f"[尝试 {attempt + 1}/{MAX_RETRIES}] 获取用户信息: {handle}")
            
            response = requests.get(
                url,
                timeout=REQUEST_TIMEOUT,
                headers={
                    'User-Agent': 'CodeDuel-Python-Service/1.0'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status') == 'OK' and data.get('result'):
                    user_info = data['result'][0]
                    print(f"✅ 成功获取用户 {handle} 的信息")
                    return user_info
                else:
                    print(f"❌ Codeforces API返回错误: {data.get('comment', '未知错误')}")
                    return None
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时 (尝试 {attempt + 1}/{MAX_RETRIES})")
        except requests.exceptions.RequestException as e:
            print(f"💥 网络请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"📄 JSON解析失败: {e}")
        except Exception as e:
            print(f"🔥 未知异常: {e}")
        
        if attempt < MAX_RETRIES - 1:
            print(f"⏳ 等待 {RETRY_DELAY} 秒后重试...")
            time.sleep(RETRY_DELAY)
    
    print(f"❌ 获取用户 {handle} 信息失败，已重试 {MAX_RETRIES} 次")
    return None

@app.route('/api/codeforces/user/firstname/<handle>', methods=['GET'])
def get_user_firstname(handle):
    """
    API端点：获取Codeforces用户的firstName和完整信息
    
    这是Java后端调用的主要接口，用于获取用户的firstName字段（用于验证）
    以及其他完整的用户信息（如头像、Rating等）。
    
    URL格式：GET /api/codeforces/user/firstname/{handle}
    
    请求参数：
        handle (str): Codeforces用户名，通过URL路径传递
        
    响应格式：
        成功时：
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "firstName": "用户的firstName",
                "handle": "用户名",
                "rating": 1500,
                "avatar": "头像URL",
                "country": "国家",
                ...
            }
        }
        
        失败时：
        {
            "success": false,
            "message": "错误信息",
            "data": null
        }
    """
    try:
        print(f"🔍 收到获取firstName请求: {handle}")
        
        if not handle or not handle.strip():
            return jsonify({
                'success': False,
                'message': 'Codeforces用户名不能为空',
                'data': None
            }), 400
        
        user_info = get_codeforces_user_info(handle.strip())
        
        if user_info:
            first_name = user_info.get('firstName', '')
            print(f"📝 用户 {handle} 的firstName: '{first_name}'")
            
            return jsonify({
                'success': True,
                'message': '获取成功',
                'data': {
                    'firstName': first_name,
                    'handle': user_info.get('handle'),
                    'rating': user_info.get('rating'),
                    'country': user_info.get('country'),
                    'avatar': user_info.get('avatar', ''),
                    'titlePhoto': user_info.get('titlePhoto', ''),
                    'organization': user_info.get('organization', ''),
                    'rank': user_info.get('rank', ''),
                    'maxRating': user_info.get('maxRating', 0)
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '获取用户信息失败，请检查用户名是否正确',
                'data': None
            }), 404
            
    except Exception as e:
        print(f"💥 处理请求时发生异常: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/user/verify', methods=['POST'])
def verify_user():
    """
    验证用户身份
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        handle = data.get('handle', '').strip()
        verification_string = data.get('verificationString', '').strip()
        
        if not handle:
            return jsonify({
                'success': False,
                'message': 'Codeforces用户名不能为空',
                'data': None
            }), 400
        
        if not verification_string:
            return jsonify({
                'success': False,
                'message': '验证字符串不能为空',
                'data': None
            }), 400
        
        print(f"🔍 验证用户身份: {handle}, 验证码: {verification_string}")
        
        user_info = get_codeforces_user_info(handle)
        
        if user_info:
            first_name = user_info.get('firstName', '')
            is_verified = first_name == verification_string
            
            print(f"📝 用户 {handle} 的firstName: '{first_name}'")
            print(f"🎯 验证结果: {'✅ 通过' if is_verified else '❌ 失败'}")
            
            return jsonify({
                'success': True,
                'message': '验证完成',
                'data': {
                    'verified': is_verified,
                    'actualFirstName': first_name,
                    'expectedVerificationString': verification_string,
                    'userInfo': {
                        'handle': user_info.get('handle'),
                        'rating': user_info.get('rating'),
                        'country': user_info.get('country'),
                        'avatar': user_info.get('avatar', ''),
                        'titlePhoto': user_info.get('titlePhoto', ''),
                        'organization': user_info.get('organization', ''),
                        'rank': user_info.get('rank', ''),
                        'maxRating': user_info.get('maxRating', 0)
                    }
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '获取用户信息失败，请检查用户名是否正确',
                'data': None
            }), 404
            
    except Exception as e:
        print(f"💥 验证过程发生异常: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/problems/update', methods=['POST'])
def update_problems():
    """
    更新题目数据

    请求参数：
    - maxPages: 最大页数（用于限制数据量）
    - minRating: 最小难度
    - maxRating: 最大难度
    """
    try:
        data = request.get_json() or {}
        max_pages = data.get('maxPages', 10)
        min_rating = data.get('minRating', 800)
        max_rating = data.get('maxRating', 3500)

        print(f"🔄 开始更新题目数据，配置: maxPages={max_pages}, rating={min_rating}-{max_rating}")

        # 获取题目数据
        problemset = get_codeforces_problemset()

        if not problemset:
            return jsonify({
                'success': False,
                'message': '获取Codeforces题目数据失败',
                'data': None
            }), 500

        problems = problemset.get('problems', [])

        # 按难度筛选
        filtered_problems = []
        for problem in problems:
            rating = problem.get('rating')
            if rating and min_rating <= rating <= max_rating:
                filtered_problems.append(problem)

        # 限制数量（模拟分页）
        max_count = max_pages * 100  # 假设每页100题
        if len(filtered_problems) > max_count:
            filtered_problems = filtered_problems[:max_count]

        print(f"✅ 筛选出 {len(filtered_problems)} 道符合条件的题目")

        return jsonify({
            'success': True,
            'message': '题目数据更新成功',
            'data': {
                'totalCount': len(filtered_problems),
                'updateTime': int(time.time()),
                'problems': filtered_problems[:10]  # 只返回前10个作为示例
            }
        })

    except Exception as e:
        print(f"❌ 更新题目数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新题目数据失败: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/tags/update', methods=['POST'])
def update_tags():
    """
    更新标签数据

    请求参数：
    - forceUpdate: 是否强制更新
    """
    try:
        data = request.get_json() or {}
        force_update = data.get('forceUpdate', False)

        print(f"🔄 开始更新标签数据，强制更新: {force_update}")

        # 获取题目数据以提取标签
        problemset = get_codeforces_problemset()

        if not problemset:
            return jsonify({
                'success': False,
                'message': '获取Codeforces数据失败',
                'data': None
            }), 500

        problems = problemset.get('problems', [])

        # 提取所有标签
        all_tags = set()
        for problem in problems:
            tags = problem.get('tags', [])
            all_tags.update(tags)

        tags_list = sorted(list(all_tags))

        print(f"✅ 提取出 {len(tags_list)} 个标签")

        return jsonify({
            'success': True,
            'message': '标签数据更新成功',
            'data': {
                'totalCount': len(tags_list),
                'updateTime': int(time.time()),
                'tags': tags_list
            }
        })

    except Exception as e:
        print(f"❌ 更新标签数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新标签数据失败: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/health', methods=['GET'])
def health_check():
    """
    健康检查
    """
    return jsonify({
        'success': True,
        'message': 'Codeforces API服务正常运行',
        'data': {
            'service': 'codeforces-api-service',
            'version': '1.0.0',
            'status': 'healthy'
        }
    })

@app.route('/api/codeforces/user/<handle>/submissions', methods=['GET'])
def get_user_submissions_api(handle):
    """获取用户提交记录"""
    try:
        print(f"🔍 获取用户 {handle} 的提交记录")

        submissions = get_user_submissions(handle)

        if submissions is None:
            return jsonify({
                'success': False,
                'message': '获取用户提交记录失败',
                'data': None
            }), 500

        # 提取已通过的题目
        solved_problems = set()
        for submission in submissions:
            if submission.get('verdict') == 'OK':
                problem = submission.get('problem', {})
                contest_id = problem.get('contestId')
                index = problem.get('index')
                if contest_id and index:
                    problem_id = f"{contest_id}-{index}"
                    solved_problems.add(problem_id)

        solved_list = sorted(list(solved_problems))

        print(f"✅ 用户 {handle} 已解决 {len(solved_list)} 道题目")

        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': {
                'solvedProblems': solved_list,
                'totalSubmissions': len(submissions)
            }
        })

    except Exception as e:
        print(f"❌ 获取用户提交记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取用户提交记录失败: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/problems/filter', methods=['POST'])
def filter_problems():
    """筛选两人都没做过的题目"""
    try:
        data = request.get_json()
        candidate_problems = data.get('candidateProblems', [])
        user1_solved = set(data.get('user1SolvedProblems', []))
        user2_solved = set(data.get('user2SolvedProblems', []))

        print(f"🔍 筛选题目，候选: {len(candidate_problems)}, 用户1已解决: {len(user1_solved)}, 用户2已解决: {len(user2_solved)}")

        # 筛选两人都没做过的题目
        unsolved_problems = []
        for problem_id in candidate_problems:
            if problem_id not in user1_solved and problem_id not in user2_solved:
                unsolved_problems.append(problem_id)

        print(f"✅ 筛选出 {len(unsolved_problems)} 道两人都没做过的题目")

        return jsonify({
            'success': True,
            'message': '筛选成功',
            'data': {
                'unsolvedProblems': unsolved_problems
            }
        })

    except Exception as e:
        print(f"❌ 筛选题目失败: {e}")
        return jsonify({
            'success': False,
            'message': f'筛选题目失败: {str(e)}',
            'data': None
        }), 500

@app.route('/api/codeforces/user/<handle>/problem/<problem_id>/status', methods=['GET'])
def check_submission_status(handle, problem_id):
    """检查用户对特定题目的提交状态"""
    try:
        print(f"🔍 检查用户 {handle} 对题目 {problem_id} 的提交状态")

        submissions = get_user_submissions(handle, count=1000)

        if submissions is None:
            return jsonify({
                'success': False,
                'message': '获取用户提交记录失败',
                'data': None
            }), 500

        # 解析题目ID
        try:
            contest_id, index = problem_id.split('-')
            contest_id = int(contest_id)
        except:
            return jsonify({
                'success': False,
                'message': '题目ID格式不正确',
                'data': None
            }), 400

        # 检查是否有通过的提交
        is_accepted = False
        for submission in submissions:
            problem = submission.get('problem', {})
            if (problem.get('contestId') == contest_id and
                problem.get('index') == index and
                submission.get('verdict') == 'OK'):
                is_accepted = True
                break

        print(f"✅ 用户 {handle} 对题目 {problem_id} 的状态: {'已通过' if is_accepted else '未通过'}")

        return jsonify({
            'success': True,
            'message': '检查完成',
            'data': {
                'isAccepted': is_accepted
            }
        })

    except Exception as e:
        print(f"❌ 检查提交状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'检查提交状态失败: {str(e)}',
            'data': None
        }), 500

@app.route('/', methods=['GET'])
def index():
    """
    首页
    """
    return jsonify({
        'service': 'Codeforces API Service',
        'version': '1.0.0',
        'endpoints': [
            'GET /api/codeforces/health - 健康检查',
            'GET /api/codeforces/user/firstname/<handle> - 获取用户firstName',
            'POST /api/codeforces/user/verify - 验证用户身份',
            'POST /api/codeforces/problems/update - 更新题目数据',
            'POST /api/codeforces/tags/update - 更新标签数据',
            'GET /api/codeforces/user/<handle>/submissions - 获取用户提交记录',
            'POST /api/codeforces/problems/filter - 筛选题目',
            'GET /api/codeforces/user/<handle>/problem/<problem_id>/status - 检查提交状态'
        ]
    })

if __name__ == '__main__':
    print("🚀 启动Codeforces API服务...")
    print("📡 服务地址: http://localhost:5000")
    print("🔗 健康检查: http://localhost:5000/api/codeforces/health")
    print("📋 API文档: http://localhost:5000")
    print()
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
