package com.xju.codeduel.event;

import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 对战结束事件
 * 
 * 当对战结束时发布此事件，用于通知其他服务进行清理工作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public class BattleEndEvent extends ApplicationEvent {

    /**
     * 参与对战的用户ID列表
     */
    private final List<Long> participantIds;

    /**
     * 房间码
     */
    private final Long roomCode;

    /**
     * 获胜者ID（可能为null表示平局）
     */
    private final Long winnerId;

    /**
     * 对战结束原因
     */
    private final String reason;

    public BattleEndEvent(Object source, List<Long> participantIds, Long roomCode, Long winnerId, String reason) {
        super(source);
        this.participantIds = participantIds;
        this.roomCode = roomCode;
        this.winnerId = winnerId;
        this.reason = reason;
    }

    public List<Long> getParticipantIds() {
        return participantIds;
    }

    public Long getRoomCode() {
        return roomCode;
    }

    public Long getWinnerId() {
        return winnerId;
    }

    public String getReason() {
        return reason;
    }
}
