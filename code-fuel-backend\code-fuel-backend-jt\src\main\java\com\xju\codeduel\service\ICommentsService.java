package com.xju.codeduel.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Comments;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xju.codeduel.model.dto.CommentDTO;
import com.xju.codeduel.model.dto.PageDTO;

/**
 * <p>
 * 评论 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface ICommentsService extends IService<Comments> {
    /**
     * 分页查询帖子下的评论
     * @param pageDTO 分页参数
     * @param postId 帖子ID
     * @return 评论DTO分页结果
     */
    Page<CommentDTO> getCommentsByPostId(PageDTO pageDTO, Integer postId);

}
