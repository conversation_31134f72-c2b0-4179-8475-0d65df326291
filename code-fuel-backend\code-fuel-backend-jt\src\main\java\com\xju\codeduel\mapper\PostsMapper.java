package com.xju.codeduel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Posts;
import com.xju.codeduel.model.dto.PostWithUserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 帖子 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface PostsMapper extends BaseMapper<Posts> {

    Page<PostWithUserDTO> pageposts(@Param("page") Page<PostWithUserDTO> page, @Param("title") String title);

    List<PostWithUserDTO> selectPostsWithUserByTitle(@Param("title") String title);

    PostWithUserDTO getPostWithUserById(@Param("id") Long id);
}
