<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.ChatMessagesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.ChatMessages">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="content" property="content" />
        <result column="sent_time" property="sentTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 用户基础结果映射（排除敏感信息） -->
    <resultMap id="UserBaseResultMap" type="com.xju.codeduel.model.domain.Users">
        <id column="user_id" property="id"/>
        <result column="codeforces_id" property="codeforcesId"/>
        <result column="avatar" property="avatar"/>
        <result column="rating" property="rating"/>
        <result column="status" property="status"/>
        <result column="ban_reason" property="banReason"/>
        <result column="register_time" property="registerTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_admin" property="isAdmin"/>
        <result column="last_login" property="lastLogin"/>
    </resultMap>

    <!-- 消息带用户信息的结果映射 -->
    <resultMap id="MessageWithUserResultMap" type="com.xju.codeduel.model.dto.MessageWithUserDTO">
        <association property="message" resultMap="BaseResultMap"/>
        <association property="user" resultMap="UserBaseResultMap"/>
    </resultMap>

    <!-- 查询消息及关联用户信息 -->
    <select id="selectMessagesWithUsers" resultMap="MessageWithUserResultMap">
        SELECT
        cm.id,
        cm.user_id,
        cm.content,
        cm.sent_time,
        cm.is_deleted,
        u.id as user_id,
        u.codeforces_id,
        u.avatar,
        u.rating,
        u.status,
        u.ban_reason,
        u.register_time,
        u.update_time,
        u.is_admin,
        u.last_login
        FROM
        chat_messages cm
        LEFT JOIN
        users u ON cm.user_id = u.id
        WHERE
        cm.is_deleted = 0
        <if test="codeforcesId != null and codeforcesId != ''">
            AND u.codeforces_id LIKE CONCAT('%', #{codeforcesId}, '%')
        </if>
        ORDER BY
        cm.sent_time ASC
    </select>

</mapper>