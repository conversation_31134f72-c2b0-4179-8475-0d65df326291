package com.xju.codeduel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户更新DTO
 * 用于接收前端发送的用户信息更新请求数据
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@ApiModel(value = "UserUpdateDTO", description = "用户信息更新请求数据")
public class UserUpdateDTO {

    @ApiModelProperty(value = "用户ID", required = true)
    private Long id;

    @ApiModelProperty(value = "Codeforces用户名", required = true)
    private String codeforcesId;

    @ApiModelProperty(value = "原密码（修改密码时需要）")
    private String oldPassword;

    @ApiModelProperty(value = "新密码（修改密码时需要）")
    private String password;

    @ApiModelProperty(value = "头像路径（修改头像时需要）")
    private String avatar;

    public UserUpdateDTO() {}

    public UserUpdateDTO(Long id, String codeforcesId, String oldPassword, String password, String avatar) {
        this.id = id;
        this.codeforcesId = codeforcesId;
        this.oldPassword = oldPassword;
        this.password = password;
        this.avatar = avatar;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeforcesId() {
        return codeforcesId;
    }

    public void setCodeforcesId(String codeforcesId) {
        this.codeforcesId = codeforcesId;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    @Override
    public String toString() {
        return "UserUpdateDTO{" +
                "id=" + id +
                ", codeforcesId='" + codeforcesId + '\'' +
                ", oldPassword='[PROTECTED]'" +
                ", password='[PROTECTED]'" +
                ", avatar='" + avatar + '\'' +
                '}';
    }
}
