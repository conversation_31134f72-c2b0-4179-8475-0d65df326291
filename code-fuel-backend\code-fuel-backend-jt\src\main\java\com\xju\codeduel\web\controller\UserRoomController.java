package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.SessionUtils;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.request.RejoinRoomRequest;
import com.xju.codeduel.service.UserRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户房间状态控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Api(tags = "用户房间状态管理")
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "http://localhost:5175", allowCredentials = "true")
public class UserRoomController {

    @Autowired
    private UserRoomService userRoomService;

    /**
     * 获取用户当前房间状态
     */
    @ApiOperation("获取用户当前房间状态")
    @GetMapping("/current-room")
    public JsonResponse<Map<String, Object>> getCurrentRoom() {
        try {
            // 获取当前登录用户
            Users currentUser = SessionUtils.getCurrentUserInfo();
            if (currentUser == null) {
                return JsonResponse.failure("用户未登录");
            }

            // 获取用户当前房间状态
            Map<String, Object> roomStatus = userRoomService.getCurrentRoomStatus(currentUser.getId());

            return JsonResponse.success(roomStatus);
        } catch (Exception e) {
            return JsonResponse.failure("获取房间状态失败: " + e.getMessage());
        }
    }

    /**
     * 重新加入房间
     */
    @ApiOperation("重新加入房间")
    @PostMapping("/rejoin-room")
    public JsonResponse<Map<String, Object>> rejoinRoom(@RequestBody RejoinRoomRequest request) {
        try {
            // 获取当前登录用户
            Users currentUser = SessionUtils.getCurrentUserInfo();
            if (currentUser == null) {
                return JsonResponse.failure("用户未登录");
            }

            // 验证并重新加入房间
            Map<String, Object> result = userRoomService.rejoinRoom(currentUser.getId(), request.getRoomCode());

            return JsonResponse.success(result);
        } catch (Exception e) {
            return JsonResponse.failure("重新加入房间失败: " + e.getMessage());
        }
    }

    /**
     * 离开当前房间
     */
    @ApiOperation("离开当前房间")
    @PostMapping("/leave-room")
    public JsonResponse<String> leaveRoom() {
        try {
            // 获取当前登录用户
            Users currentUser = SessionUtils.getCurrentUserInfo();
            if (currentUser == null) {
                return JsonResponse.failure("用户未登录");
            }

            // 离开房间
            userRoomService.leaveRoom(currentUser.getId());

            return JsonResponse.success("成功离开房间");
        } catch (Exception e) {
            return JsonResponse.failure("离开房间失败: " + e.getMessage());
        }
    }

    /**
     * 强制清理用户房间状态
     */
    @ApiOperation("强制清理用户房间状态")
    @PostMapping("/clear-status")
    public JsonResponse<String> clearUserRoomStatus(@RequestBody Map<String, Object> requestData) {
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());

            // 强制清理用户房间状态
            userRoomService.clearUserRoomStatus(userId);

            return JsonResponse.success("用户房间状态已清理");
        } catch (Exception e) {
            return JsonResponse.failure("清理用户房间状态失败: " + e.getMessage());
        }
    }
}
