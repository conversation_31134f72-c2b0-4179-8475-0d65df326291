package com.xju.codeduel.websocket;

import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.service.IRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * 房间WebSocket消息处理器
 * 
 * 功能说明：
 * 1. 处理房间相关的WebSocket消息
 * 2. 实现房间内实时通信和状态同步
 * 3. 支持用户加入、离开、状态更新等操作
 * 
 * 消息类型：
 * - JOIN: 用户加入房间
 * - LEAVE: 用户离开房间
 * - STATUS_UPDATE: 房间状态更新
 * - BATTLE_START: 对战开始
 * - BATTLE_END: 对战结束
 * 
 * 通信机制：
 * - 客户端发送消息到 /app/room/* 
 * - 服务端广播消息到 /topic/room/{roomCode}
 * - 支持房间内所有用户实时接收消息
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Controller
public class RoomWebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(RoomWebSocketHandler.class);

    @Autowired
    private IRoomService roomService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * 处理用户加入房间消息
     * 
     * 消息流程：
     * 1. 客户端发送消息到 /app/room/{roomCode}/join
     * 2. 服务端处理加入逻辑
     * 3. 广播更新消息到 /topic/room/{roomCode}
     * 4. 房间内所有用户收到更新通知
     * 
     * @param roomCode 房间码
     * @param message 加入消息（包含用户信息）
     * @return 房间更新消息
     */
    @MessageMapping("/room/{roomCode}/join")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleJoinRoom(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        
        logger.info("🚪 WebSocket: 用户加入房间 {}, 消息: {}", roomCode, message);
        
        try {
            // 获取最新房间信息
            RoomInfoDTO roomInfo = roomService.getRoomInfo(roomCode);
            
            if (roomInfo != null) {
                // 创建房间更新消息
                RoomUpdateMessage updateMessage = new RoomUpdateMessage();
                updateMessage.setType("USER_JOINED");
                updateMessage.setRoomInfo(roomInfo);
                updateMessage.setMessage("用户 " + message.get("userName") + " 加入了房间");
                updateMessage.setTimestamp(System.currentTimeMillis());
                
                logger.info("✅ WebSocket: 房间 {} 状态已更新", roomCode);
                return updateMessage;
            }
            
        } catch (Exception e) {
            logger.error("❌ WebSocket: 处理加入房间消息失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 处理用户离开房间消息
     * 
     * @param roomCode 房间码
     * @param message 离开消息（包含用户信息）
     * @return 房间更新消息
     */
    @MessageMapping("/room/{roomCode}/leave")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleLeaveRoom(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        
        logger.info("🚪 WebSocket: 用户离开房间 {}, 消息: {}", roomCode, message);
        
        try {
            // 获取最新房间信息
            RoomInfoDTO roomInfo = roomService.getRoomInfo(roomCode);
            
            if (roomInfo != null) {
                // 创建房间更新消息
                RoomUpdateMessage updateMessage = new RoomUpdateMessage();
                updateMessage.setType("USER_LEFT");
                updateMessage.setRoomInfo(roomInfo);
                updateMessage.setMessage("用户 " + message.get("userName") + " 离开了房间");
                updateMessage.setTimestamp(System.currentTimeMillis());
                
                logger.info("✅ WebSocket: 房间 {} 状态已更新", roomCode);
                return updateMessage;
            }
            
        } catch (Exception e) {
            logger.error("❌ WebSocket: 处理离开房间消息失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 处理对战开始消息
     * 
     * @param roomCode 房间码
     * @param message 开始消息
     * @return 房间更新消息
     */
    @MessageMapping("/room/{roomCode}/start")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleStartBattle(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        
        logger.info("⚔️ WebSocket: 开始对战 {}, 消息: {}", roomCode, message);
        
        try {
            // 调用服务层开始对战
            RoomInfoDTO roomInfo = roomService.startBattle(roomCode);
            
            if (roomInfo != null) {
                // 创建房间更新消息
                RoomUpdateMessage updateMessage = new RoomUpdateMessage();
                updateMessage.setType("BATTLE_STARTED");
                updateMessage.setRoomInfo(roomInfo);
                updateMessage.setMessage("对战开始！");
                updateMessage.setTimestamp(System.currentTimeMillis());
                
                logger.info("✅ WebSocket: 房间 {} 对战已开始", roomCode);
                return updateMessage;
            }
            
        } catch (Exception e) {
            logger.error("❌ WebSocket: 处理开始对战消息失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 处理平局申请和回应消息
     *
     * 消息流程：
     * 1. 客户端发送平局申请/回应到 /app/room/{roomCode}/draw
     * 2. 服务端转发消息到 /topic/room/{roomCode}/draw
     * 3. 房间内其他用户收到平局通知
     *
     * @param roomCode 房间码
     * @param message 平局消息（包含类型、用户信息等）
     * @return 平局消息（直接转发）
     */
    @MessageMapping("/room/{roomCode}/draw")
    @SendTo("/topic/room/{roomCode}/draw")
    public Map<String, Object> handleDrawMessage(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {

        String messageType = (String) message.get("type");
        logger.info("🤝 WebSocket: 房间 {} 收到平局消息，类型: {}", roomCode, messageType);

        try {
            if ("DRAW_REQUEST".equals(messageType)) {
                String requesterName = (String) message.get("requesterName");
                logger.info("📤 WebSocket: 用户 {} 在房间 {} 申请平局", requesterName, roomCode);
            } else if ("DRAW_RESPONSE".equals(messageType)) {
                String responderName = (String) message.get("responderName");
                Boolean accept = (Boolean) message.get("accept");
                logger.info("📥 WebSocket: 用户 {} 在房间 {} {} 平局申请",
                    responderName, roomCode, accept ? "同意" : "拒绝");
            }

            // 直接转发消息给房间内所有用户
            return message;

        } catch (Exception e) {
            logger.error("❌ WebSocket: 处理平局消息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 广播房间状态更新
     *
     * 功能说明：
     * 1. 主动向房间内所有用户广播状态更新
     * 2. 用于服务端主动推送消息
     * 3. 支持各种房间状态变化通知
     *
     * @param roomCode 房间码
     * @param updateMessage 更新消息
     */
    public void broadcastRoomUpdate(Long roomCode, RoomUpdateMessage updateMessage) {
        logger.info("📢 WebSocket: 广播房间 {} 状态更新", roomCode);

        try {
            // 向房间内所有订阅者发送消息
            messagingTemplate.convertAndSend("/topic/room/" + roomCode, updateMessage);

            logger.debug("✅ WebSocket: 房间 {} 状态更新已广播", roomCode);

        } catch (Exception e) {
            logger.error("❌ WebSocket: 广播房间状态更新失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 房间更新消息类
     * 
     * 用于封装WebSocket传输的房间更新信息
     */
    public static class RoomUpdateMessage {
        private String type;           // 消息类型
        private RoomInfoDTO roomInfo;  // 房间信息
        private String message;        // 描述消息
        private Long timestamp;        // 时间戳

        // ==================== Getter和Setter方法 ====================

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public RoomInfoDTO getRoomInfo() {
            return roomInfo;
        }

        public void setRoomInfo(RoomInfoDTO roomInfo) {
            this.roomInfo = roomInfo;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return "RoomUpdateMessage{" +
                    "type='" + type + '\'' +
                    ", message='" + message + '\'' +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }
}
