<template>
  <el-dialog
    v-model="dialogVisible"
    title="Codeforces 身份验证"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="verification-content">
      <div class="step-indicator">
        <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <div class="step-number">1</div>
          <div class="step-text">修改资料</div>
        </div>
        <div class="step-line" :class="{ completed: currentStep > 1 }"></div>
        <div class="step" :class="{ active: currentStep >= 2 }">
          <div class="step-number">2</div>
          <div class="step-text">注册并验证</div>
        </div>
      </div>

      <!-- 步骤1: 修改资料 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>步骤 1: 修改资料</h3>
        <p class="instruction">
          请复制下面的验证字符串，然后前往 Codeforces 修改您的个人资料：
        </p>
        <div class="verification-string-container">
          <el-input
            v-model="verificationString"
            readonly
            class="verification-input"
            placeholder="正在生成验证字符串..."
          >
            <template #append>
              <el-button @click="copyToClipboard" :disabled="!verificationString">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="instruction-steps">
          <p><strong>操作步骤：</strong></p>
          <ol>
            <li>点击上方的"复制"按钮复制验证字符串</li>
            <li>前往 <a href="https://codeforces.com/settings/social" target="_blank">Codeforces 个人设置页面</a></li>
            <li>将 <strong>First name</strong> 字段修改为复制的验证字符串</li>
            <li>保存设置后，点击下方的"下一步"按钮</li>
          </ol>
        </div>
      </div>

      <!-- 步骤2: 注册并验证 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>步骤 2: 注册并验证</h3>
        <p class="instruction">
          请确保您已经在 Codeforces 上将 First name 修改为验证字符串，然后点击"注册并验证"按钮。
          验证成功后将自动完成账户注册并跳转到登录页面。
        </p>
        <div class="verification-info">
          <p><strong>您的 Codeforces ID:</strong> {{ codeforcesId }}</p>
          <p><strong>验证字符串:</strong> {{ verificationString }}</p>
        </div>
        <div class="verification-status" v-if="verificationStatus">
          <el-alert
            :title="verificationStatus.message"
            :type="verificationStatus.type"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 注册成功状态（在第2步中显示） -->
      <div v-if="currentStep === 2 && registrationSuccess" class="step-content">
        <h3>注册成功！</h3>
        <div class="success-content">
          <el-icon class="success-icon" color="#67C23A"><SuccessFilled /></el-icon>
          <p>🎉 恭喜您！账户注册成功，即将跳转到登录页面...</p>
          <p class="countdown-text">{{ countdownText }}</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isProcessing">取消</el-button>
        <el-button 
          v-if="currentStep === 1" 
          type="primary" 
          @click="nextStep"
          :disabled="!verificationString"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2 && !registrationSuccess"
          type="primary"
          @click="verifyAndRegister"
          :loading="isVerifying"
        >
          注册并验证
        </el-button>
        <el-button
          v-if="currentStep === 2 && registrationSuccess"
          type="primary"
          @click="goToLogin"
          :disabled="countdown > 0"
        >
          {{ countdown > 0 ? `${countdown}秒后跳转` : '立即跳转到登录' }}
        </el-button>

      </div>
    </template>
  </el-dialog>
</template>

<!--
  Codeforces身份验证对话框组件

  ==================== 组件概述 ====================
  这是用户注册流程中的核心组件，负责处理Codeforces身份验证和用户注册。
  采用两步式设计，简化用户操作，提升注册体验。

  ==================== 功能特性 ====================
  1. 两步验证流程：
     - 步骤1：修改资料（生成验证码，指导用户修改Codeforces资料）
     - 步骤2：注册并验证（一键完成验证和注册）

  2. 智能状态管理：
     - 使用registrationSuccess控制页面显示
     - 注册成功后在第2步显示成功状态
     - 5秒倒计时自动跳转到登录页

  3. 完整的用户引导：
     - 清晰的步骤指示器
     - 详细的操作说明
     - 一键复制验证码功能
     - 直接链接到Codeforces设置页面

  ==================== 技术架构 ====================
  验证原理：
  - 用户将系统生成的验证字符串设置为Codeforces的firstName字段
  - 系统调用后端的合并接口 /verify-codeforces
  - 后端通过Python服务获取Codeforces用户信息
  - 验证firstName匹配后直接完成用户注册
  - 前端显示成功状态并自动跳转

  数据流向：
  前端 → Java后端 → Python服务 → Codeforces API
    ↓       ↓          ↓           ↓
  UI更新 ← 注册用户 ← 获取用户信息 ← 返回用户数据

  ==================== 设计优势 ====================
  1. 流程简化：从3步减少到2步，减少用户操作
  2. 避免重复：验证和注册合并，防止重复数据库操作
  3. 自动化：成功后自动跳转，无需手动操作
  4. 用户友好：清晰的状态反馈和错误处理
-->
<script setup>
// ==================== 导入依赖 ====================
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, SuccessFilled } from '@element-plus/icons-vue'
import { testRegister } from '@/api/api'
import { useRouter } from 'vue-router'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  codeforcesId: {
    type: String,
    required: true
  },
  password: {
    type: String,
    required: true
  },
  verificationCode: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'success', 'cancel'])

// ==================== 响应式变量 ====================
const router = useRouter()
const dialogVisible = ref(false)
const currentStep = ref(1)
const verificationString = ref('')
const isVerifying = ref(false)
const isProcessing = ref(false)
const verificationStatus = ref(null)
const countdown = ref(0)
const countdownText = ref('')
const registrationSuccess = ref(false) // 注册成功状态

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetDialog()
    verificationString.value = props.verificationCode
  }
})

// 监听验证码变化
watch(() => props.verificationCode, (newVal) => {
  verificationString.value = newVal
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置对话框状态
const resetDialog = () => {
  currentStep.value = 1
  verificationString.value = ''
  isVerifying.value = false
  isProcessing.value = false
  verificationStatus.value = null
  registrationSuccess.value = false
  countdown.value = 0
  countdownText.value = ''
}

// ==================== 工具函数 ====================
// 注意：原来的 verifyCodeforcesUser 方法已删除，现在使用后端的合并接口

/**
 * 复制验证字符串到剪贴板
 *
 * 使用现代的Clipboard API，如果不支持则降级到传统方法
 * 提供良好的用户体验和兼容性
 */
const copyToClipboard = async () => {
  try {
    // 优先使用现代的Clipboard API
    await navigator.clipboard.writeText(verificationString.value)
    ElMessage.success('✅ 验证字符串已复制到剪贴板')
  } catch (error) {
    // 降级方案：使用传统的document.execCommand（虽然已弃用但兼容性好）
    try {
      const textArea = document.createElement('textarea')
      textArea.value = verificationString.value
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)

      if (successful) {
        ElMessage.success('✅ 验证字符串已复制到剪贴板')
      } else {
        throw new Error('复制命令执行失败')
      }
    } catch (fallbackError) {
      console.error('复制失败:', fallbackError)
      ElMessage.error('❌ 复制失败，请手动复制验证字符串')
    }
  }
}

// 下一步
const nextStep = () => {
  currentStep.value = 2
}

/**
 * 验证并注册用户（合并操作）
 *
 * 这个方法将验证和注册合并为一个步骤，避免重复操作：
 * 1. 首先验证用户是否已正确设置Codeforces的firstName字段
 * 2. 验证成功后直接调用后端的验证并注册接口
 * 3. 注册成功后显示成功页面并启动倒计时跳转
 *
 * 技术要点：
 * - 使用后端的 /verify-codeforces 接口（已合并验证和注册逻辑）
 * - 避免了前端验证成功后再次调用注册接口的重复操作
 * - 提供详细的错误处理和用户反馈
 * - 成功后自动跳转到登录页面
 */
const verifyAndRegister = async () => {
  console.log('🚀 开始验证并注册用户...')

  // 设置验证状态，显示加载动画
  isVerifying.value = true
  verificationStatus.value = null

  try {
    console.log('📋 验证并注册参数:', {
      codeforcesId: props.codeforcesId,
      password: '***', // 不记录密码
      verificationString: verificationString.value
    })

    // 准备请求数据
    const registerData = {
      codeforcesId: props.codeforcesId,
      password: props.password,
      verificationString: verificationString.value
    }

    console.log('📤 调用后端验证并注册接口: /api/users/verify-codeforces')

    // 调用后端的合并接口（验证+注册）
    const response = await fetch('/api/users/verify-codeforces', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    })

    console.log('📥 后端响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const responseData = await response.json()
    console.log('📥 后端响应数据:', responseData)

    // 检查后端返回的状态
    if (responseData.status === true) {
      console.log('✅ 验证并注册成功！')

      // 设置成功状态
      verificationStatus.value = {
        type: 'success',
        message: '🎉 验证并注册成功！正在跳转到登录页面...'
      }

      // 设置注册成功状态（在第2步显示成功页面）
      registrationSuccess.value = true

      // 启动倒计时跳转
      startCountdown()

      // 显示成功消息
      ElMessage.success('🎉 注册成功！欢迎加入CodeDuel！')

    } else {
      console.log('❌ 验证或注册失败:', responseData.message)

      // 设置失败状态
      verificationStatus.value = {
        type: 'error',
        message: responseData.message || '验证或注册失败，请重试'
      }

      // 显示错误消息
      ElMessage.error(`❌ ${responseData.message || '验证或注册失败'}`)
    }

  } catch (error) {
    console.error('💥 验证并注册过程中发生异常:', error)

    // 设置错误状态
    verificationStatus.value = {
      type: 'error',
      message: '网络连接失败，请检查网络后重试'
    }

    // 显示错误消息
    ElMessage.error('❌ 网络连接失败，请重试')

  } finally {
    // 重置验证状态
    isVerifying.value = false
    console.log('🔄 验证并注册流程结束')
  }
}

/**
 * 启动倒计时跳转
 * 注册成功后自动倒计时跳转到登录页面
 */
const startCountdown = () => {
  console.log('⏰ 启动倒计时跳转')
  countdown.value = 5 // 5秒倒计时

  const timer = setInterval(() => {
    countdown.value--
    countdownText.value = `${countdown.value} 秒后自动跳转到登录页面`

    if (countdown.value <= 0) {
      clearInterval(timer)
      goToLogin()
    }
  }, 1000)

  // 初始化倒计时文本
  countdownText.value = `${countdown.value} 秒后自动跳转到登录页面`
}

/**
 * 跳转到登录页面
 * 关闭对话框并导航到登录页面
 */
const goToLogin = () => {
  console.log('🔄 跳转到登录页面')

  // 关闭对话框
  dialogVisible.value = false

  // 触发成功事件（通知父组件）
  emit('success')

  // 跳转到登录页面
  router.push('/login')

  // 显示提示消息
  ElMessage.success('请使用您的Codeforces用户名和密码登录')
}

// 已删除 completeRegistration 方法，功能已合并到 verifyAndRegister 中

// /**
//  * 测试注册（跳过Codeforces验证）
//  * 用于开发和测试环境
//  */
// const testRegistration = async () => {
//   console.log('🧪 开始测试注册流程（跳过验证）...')
//
//   isProcessing.value = true
//
//   try {
//     const registerData = {
//       codeforcesId: props.codeforcesId,
//       password: props.password,
//       verificationString: verificationString.value
//     }
//
//     console.log('📤 发送测试注册请求:', registerData)
//
//     const response = await testRegister(registerData)
//
//     console.log('📥 测试注册响应:', response)
//
//     if (response && response.data && response.data.status === true) {
//       console.log('✅ 测试注册成功')
//       ElMessage.success('🧪 测试注册成功！')
//
//       // 设置注册成功状态
//       registrationSuccess.value = true
//
//       // 启动倒计时跳转
//       startCountdown()
//     } else {
//       const errorMessage = response?.data?.message || '测试注册失败，请重试'
//       console.log('❌ 测试注册失败:', errorMessage)
//       ElMessage.error(`❌ ${errorMessage}`)
//     }
//   } catch (error) {
//     console.error('💥 测试注册过程中发生异常:', error)
//
//     if (error.response && error.response.data && error.response.data.message) {
//       ElMessage.error(`❌ ${error.response.data.message}`)
//     } else {
//       ElMessage.error('❌ 测试注册过程中发生错误，请重试')
//     }
//   } finally {
//     isProcessing.value = false
//   }
// }

// 取消
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}
</script>

<style scoped>
.verification-content {
  padding: 20px 0;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #dcdfe6;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #409eff;
  color: white;
}

.step.completed .step-number {
  background-color: #67c23a;
  color: white;
}

.step-text {
  font-size: 12px;
  color: #909399;
}

.step.active .step-text {
  color: #409eff;
  font-weight: bold;
}

.step.completed .step-text {
  color: #67c23a;
}

.step-line {
  width: 80px;
  height: 2px;
  background-color: #dcdfe6;
  margin: 0 20px;
  margin-bottom: 24px;
  transition: all 0.3s;
}

.step-line.completed {
  background-color: #67c23a;
}

.step-content {
  text-align: center;
}

.step-content h3 {
  margin-bottom: 16px;
  color: #303133;
}

.instruction {
  margin-bottom: 20px;
  color: #606266;
  line-height: 1.6;
}

.verification-string-container {
  margin-bottom: 20px;
}

.verification-input {
  font-family: 'Courier New', monospace;
}

.instruction-steps {
  text-align: left;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-top: 20px;
}

.instruction-steps p {
  margin-bottom: 8px;
  font-weight: bold;
}

.instruction-steps ol {
  margin: 0;
  padding-left: 20px;
}

.instruction-steps li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.instruction-steps a {
  color: #409eff;
  text-decoration: none;
}

.instruction-steps a:hover {
  text-decoration: underline;
}

.verification-info {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.verification-info p {
  margin: 8px 0;
  word-break: break-all;
}

.verification-status {
  margin-top: 16px;
}

.success-content {
  padding: 20px;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.countdown-text {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
