package com.xju.codeduel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.BattleRecords;
import com.xju.codeduel.model.dto.BattleRecordWithDetailsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 对战记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface BattleRecordsMapper extends BaseMapper<BattleRecords> {

    /**
     * 获取最近的对战记录（包含题目和参与者信息）
     * @param page 分页参数
     * @return 对战记录详情列表
     */
    Page<BattleRecordWithDetailsDTO> getRecentBattleRecords(@Param("page") Page<BattleRecordWithDetailsDTO> page);

}
