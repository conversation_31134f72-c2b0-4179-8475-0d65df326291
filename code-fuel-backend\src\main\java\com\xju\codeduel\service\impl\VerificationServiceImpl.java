package com.xju.codeduel.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xju.codeduel.service.IVerificationService;
import com.xju.codeduel.model.dto.CodeforcesUserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.security.SecureRandom;
import java.time.Duration;

/**
 * 验证码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class VerificationServiceImpl implements IVerificationService {

    private static final Logger logger = LoggerFactory.getLogger(VerificationServiceImpl.class);
    
    private static final String PYTHON_SERVICE_BASE = "http://localhost:5000/api/codeforces";
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int VERIFICATION_CODE_LENGTH = 16;
    
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final SecureRandom secureRandom;

    public VerificationServiceImpl() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
        this.secureRandom = new SecureRandom();
    }

    @Override
    public String generateVerificationCode() {
        StringBuilder sb = new StringBuilder(VERIFICATION_CODE_LENGTH);
        
        for (int i = 0; i < VERIFICATION_CODE_LENGTH; i++) {
            int randomIndex = secureRandom.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        
        String verificationCode = sb.toString();
        logger.info("生成验证码: {}", verificationCode);
        
        return verificationCode;
    }

    @Override
    public boolean verifyCodeforcesUser(String codeforcesId, String verificationString) {
        try {
            logger.info("开始验证Codeforces用户: {} 验证码: {}", codeforcesId, verificationString);

            // 构建Python服务API请求URL
            String apiUrl = PYTHON_SERVICE_BASE + "/user/verify";

            // 构建请求体
            String requestBody = String.format(
                "{\"handle\":\"%s\",\"verificationString\":\"%s\"}",
                codeforcesId, verificationString
            );

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "CodeDuel-Backend/1.0")
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Python服务请求失败: HTTP {}", response.statusCode());
                return false;
            }

            // 解析响应
            JsonNode jsonResponse = objectMapper.readTree(response.body());

            if (!jsonResponse.get("success").asBoolean()) {
                logger.error("Python服务返回错误: {}",
                        jsonResponse.get("message").asText());
                return false;
            }

            // 获取验证结果
            JsonNode data = jsonResponse.get("data");
            if (data == null) {
                logger.error("Python服务返回数据为空");
                return false;
            }

            boolean isVerified = data.get("verified").asBoolean();
            String actualFirstName = data.get("actualFirstName").asText();

            logger.info("用户 {} 的firstName: {}", codeforcesId, actualFirstName);
            logger.info("验证字符串: {}", verificationString);

            if (isVerified) {
                logger.info("用户 {} 验证成功", codeforcesId);
            } else {
                logger.warn("用户 {} 验证失败，firstName不匹配", codeforcesId);
            }

            return isVerified;

        } catch (IOException e) {
            logger.error("网络请求异常: {}", e.getMessage());
            return false;
        } catch (InterruptedException e) {
            logger.error("请求被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            logger.error("验证过程发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getCodeforcesUserFirstName(String codeforcesId) {
        try {
            logger.info("获取Codeforces用户firstName: {}", codeforcesId);

            // 构建Python服务API请求URL
            String apiUrl = PYTHON_SERVICE_BASE + "/user/firstname/" + codeforcesId;

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "CodeDuel-Backend/1.0")
                    .GET()
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Python服务请求失败: HTTP {}", response.statusCode());
                return null;
            }

            // 解析响应
            JsonNode jsonResponse = objectMapper.readTree(response.body());

            if (!jsonResponse.get("success").asBoolean()) {
                logger.error("Python服务返回错误: {}",
                        jsonResponse.get("message").asText());
                return null;
            }

            // 获取firstName
            JsonNode data = jsonResponse.get("data");
            if (data == null) {
                logger.error("Python服务返回数据为空");
                return null;
            }

            String firstName = data.get("firstName").asText();

            logger.info("用户 {} 的firstName: {}", codeforcesId, firstName);

            return firstName;

        } catch (IOException e) {
            logger.error("网络请求异常: {}", e.getMessage());
            return null;
        } catch (InterruptedException e) {
            logger.error("请求被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            logger.error("获取firstName过程发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public CodeforcesUserInfo getCodeforcesUserInfo(String codeforcesId) {
        try {
            logger.info("获取Codeforces用户完整信息: {}", codeforcesId);

            // 构建Python服务API请求URL
            String apiUrl = PYTHON_SERVICE_BASE + "/user/firstname/" + codeforcesId;

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "CodeDuel-Backend/1.0")
                    .GET()
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Python服务请求失败: HTTP {}", response.statusCode());
                return null;
            }

            // 解析响应
            JsonNode jsonResponse = objectMapper.readTree(response.body());

            if (!jsonResponse.get("success").asBoolean()) {
                logger.error("Python服务返回错误: {}",
                        jsonResponse.get("message").asText());
                return null;
            }

            // 获取用户信息
            JsonNode data = jsonResponse.get("data");
            if (data == null) {
                logger.error("Python服务返回数据为空");
                return null;
            }

            // 构建CodeforcesUserInfo对象
            CodeforcesUserInfo userInfo = new CodeforcesUserInfo();
            userInfo.setHandle(data.get("handle") != null ? data.get("handle").asText() : "");
            userInfo.setFirstName(data.get("firstName") != null ? data.get("firstName").asText() : "");
            userInfo.setAvatar(data.get("avatar") != null ? data.get("avatar").asText() : "");
            userInfo.setTitlePhoto(data.get("titlePhoto") != null ? data.get("titlePhoto").asText() : "");
            userInfo.setRating(data.get("rating") != null ? data.get("rating").asInt() : 1500);
            userInfo.setMaxRating(data.get("maxRating") != null ? data.get("maxRating").asInt() : 1500);
            userInfo.setCountry(data.get("country") != null ? data.get("country").asText() : "");
            userInfo.setOrganization(data.get("organization") != null ? data.get("organization").asText() : "");
            userInfo.setRank(data.get("rank") != null ? data.get("rank").asText() : "");

            logger.info("成功获取用户 {} 的完整信息: {}", codeforcesId, userInfo);

            return userInfo;

        } catch (IOException e) {
            logger.error("网络请求异常: {}", e.getMessage());
            return null;
        } catch (InterruptedException e) {
            logger.error("请求被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            logger.error("获取用户信息过程发生异常: {}", e.getMessage(), e);
            return null;
        }
    }
}
