package com.xju.codeduel.service;

import java.util.Map;

/**
 * 用户房间状态服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface UserRoomService {

    /**
     * 获取用户当前房间状态
     * 
     * @param userId 用户ID
     * @return 房间状态信息
     */
    Map<String, Object> getCurrentRoomStatus(Long userId);

    /**
     * 重新加入房间
     * 
     * @param userId 用户ID
     * @param roomCode 房间号
     * @return 房间信息
     */
    Map<String, Object> rejoinRoom(Long userId, Long roomCode);

    /**
     * 离开房间
     * 
     * @param userId 用户ID
     */
    void leaveRoom(Long userId);

    /**
     * 更新用户房间状态
     * 
     * @param userId 用户ID
     * @param roomCode 房间号
     * @param status 房间状态
     */
    void updateUserRoomStatus(Long userId, Long roomCode, String status);

    /**
     * 清除用户房间状态
     *
     * @param userId 用户ID
     */
    void clearUserRoomStatus(Long userId);

    /**
     * 检查用户是否在房间中
     *
     * @param userId 用户ID
     * @return 是否在房间中
     */
    boolean isUserInRoom(Long userId);
}
