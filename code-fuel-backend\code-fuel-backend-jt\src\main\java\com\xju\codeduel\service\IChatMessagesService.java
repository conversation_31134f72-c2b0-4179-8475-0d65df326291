package com.xju.codeduel.service;

import com.xju.codeduel.model.domain.ChatMessages;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xju.codeduel.model.dto.MessageWithUserDTO;

import java.util.List;

/**
 * <p>
 * 聊天消息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IChatMessagesService extends IService<ChatMessages> {

    List<MessageWithUserDTO> getMessagesWithUsers(String codeforcesId);
}
