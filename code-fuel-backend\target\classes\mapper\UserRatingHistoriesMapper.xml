<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.UserRatingHistoriesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.UserRatingHistories">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="battle_id" property="battleId" />
        <result column="old_rating" property="oldRating" />
        <result column="new_rating" property="newRating" />
        <result column="reason" property="reason" />
        <result column="record_time" property="recordTime" />
    </resultMap>

</mapper>
