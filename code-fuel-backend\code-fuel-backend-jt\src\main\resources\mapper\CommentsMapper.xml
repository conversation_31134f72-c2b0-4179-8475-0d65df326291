<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.CommentsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xju.codeduel.model.domain.Comments">
        <id column="id" property="id" />
        <result column="post_id" property="postId" />
        <result column="user_id" property="userId" />
        <result column="parent_id" property="parentId" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    <select id="getCommentsByPostId" resultType="com.xju.codeduel.model.dto.CommentDTO">
        SELECT
            c.id as commentId,
            c.user_id as userId,
            u.codeforces_id as username,
            u.avatar as userAvatar,
            pu.codeforces_id as parentUsername,
            c.content as content,
            c.create_time as createTime,
            c.parent_id as parentId
        FROM comments c
                 LEFT JOIN users u ON c.user_id = u.id
                 LEFT JOIN comments pc ON c.parent_id = pc.id
                 LEFT JOIN users pu ON pc.user_id = pu.id
        WHERE c.post_id = #{postId}
          AND c.is_deleted = 0
        ORDER BY c.create_time ASC
    </select>

</mapper>
