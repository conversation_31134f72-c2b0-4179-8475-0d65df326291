package com.xju.codeduel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.dto.UserDTO;
import com.xju.codeduel.model.dto.UserRatingChangeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface UsersMapper extends BaseMapper<Users> {


    Page<Users> pagelist(@Param("page") Page<Users> page,@Param("user") UserDTO users);


    /**
     * 根据用户名模糊查询用户及其Rating变化历史
     * @param username 用户名关键词
     * @return 用户Rating变化DTO列表
     */
    Page<UserRatingChangeDTO> getUsersWithRatingHistory(@Param("page") Page<UserRatingChangeDTO> page, @Param("username") String username);

    /**
     * 获取用户列表（仅基本信息，包含对战记录条数）
     * @param page 分页参数
     * @param username 用户名关键词（可选）
     * @return 用户列表
     */
    Page<UserDTO> getUsersListWithBattleCount(@Param("page") Page<UserDTO> page, @Param("username") String username);

    /**
     * 根据用户名精确查询用户及其完整Rating历史记录
     * @param username 用户名（精确匹配）
     * @return 用户Rating变化DTO列表
     */
    List<UserRatingChangeDTO> getUserRatingHistoryByUsername(@Param("username") String username);


}
