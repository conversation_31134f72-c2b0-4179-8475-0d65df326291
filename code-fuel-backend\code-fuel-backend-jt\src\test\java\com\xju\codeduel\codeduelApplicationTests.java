package com.xju.codeduel;

import com.xju.codeduel.mapper.UsersMapper;
import com.xju.codeduel.model.domain.Users;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class codeduelApplicationTests extends BaseTest {
    @Autowired
    private UsersMapper usersMapper;
    @Test
    void contextLoads() {
    }

    @Test
        // 插入用户名为helloi，密码是helloi，i 1 - 20
    void insertusers() {
        for (int i = 1; i <= 20; i++) {
            // 初始化 Users 对象
            Users user = new Users();
            user.setCodeforcesId("helloi" + i).setPassword("helloi" + i).setAvatar("https://userpic.codeforces.org/no-title.jpg");
            // 使用 mapper 插入用户数据
            usersMapper.insert(user);
        }
    }


}
