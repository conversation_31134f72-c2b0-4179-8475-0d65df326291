package com.xju.codeduel.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.model.domain.Posts;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.PostWithUserDTO;

import java.util.List;

/**
 * <p>
 * 帖子 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IPostsService extends IService<Posts> {
    List<PostWithUserDTO> getPostsWithUserByTitle(String title);

    Page<PostWithUserDTO> pageposts(PageDTO pageDto, String title);

    PostWithUserDTO getPostWithUserById(Long id);
}